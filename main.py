import asyncio
import gc
import logging
import logging.config
import os
from contextlib import asynccontextmanager

import psutil
import uvicorn
from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from app.api.routers.health import health_router
from app.api.routers.v1.job_post import parse_router
from app.api.routers.v2.job_post import generate_router_v2, parse_router_v2
from app.container import get_service_container
from app.utils import LOGGING_CONFIG

# Load environment variables once at startup
load_dotenv()

os.makedirs("logs", exist_ok=True)
logging.config.dictConfig(LOGGING_CONFIG)

logger = logging.getLogger(__name__)


async def periodic_cleanup():
    """Perform periodic cleanup tasks to prevent memory leaks."""
    # Memory threshold for aggressive cleanup (MB)
    MEMORY_WARNING_THRESHOLD = 500  # MB
    MEMORY_CRITICAL_THRESHOLD = 1000  # MB

    while True:
        try:
            await asyncio.sleep(15)  # Run every 15 seconds for aggressive cleanup

            logger.info("Starting periodic cleanup...")

            # Get memory before cleanup
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024

            container = get_service_container()

            # Force garbage collection with multiple passes for thorough cleanup
            collected_total = 0
            for _ in range(3):  # Multiple GC passes for stubborn references
                collected_total += gc.collect()

            # Memory pressure-based cleanup
            if memory_before > MEMORY_WARNING_THRESHOLD:
                logger.warning(f"High memory usage detected: {memory_before:.2f}MB")

                # Clear all caches due to memory pressure
                from app.core.job_post.parse.utils import clear_all_caches

                clear_all_caches()

                # Clear LLM service cache
                container.llm_service.clear_cache()
                logger.info("Cleared all caches due to high memory usage")

                # Monitor browser processes if browser pool exists
                if (
                    hasattr(container, "_browser_pool_service")
                    and container._browser_pool_service
                ):
                    await container._browser_pool_service.monitor_browser_processes()
                    await container._browser_pool_service.health_check()

                # Force additional garbage collection
                for _ in range(2):  # Extra GC passes for high memory
                    collected_total += gc.collect()

            # Critical memory usage - more aggressive cleanup
            if memory_before > MEMORY_CRITICAL_THRESHOLD:
                logger.critical(f"Critical memory usage: {memory_before:.2f}MB")

                # Even more aggressive cleanup
                import sys

                if hasattr(sys, "intern"):
                    # Clear interned strings cache
                    pass  # Python manages this internally

                # Force cleanup of compiled graph caches
                from app.core.job_post.parse import graph

                if hasattr(graph, "_compiled_graph") and graph._compiled_graph:
                    logger.info("Clearing compiled graph due to critical memory usage")
                    graph._compiled_graph = None

                # Additional GC passes
                for _ in range(5):
                    collected_total += gc.collect()

            logger.info(f"Garbage collection freed {collected_total} objects")

            # Get current memory usage after cleanup
            memory_after = process.memory_info().rss / 1024 / 1024
            memory_freed = memory_before - memory_after
            logger.info(
                f"Memory usage after cleanup: {memory_after:.2f}MB (freed: {memory_freed:.2f}MB)"
            )
            logger.info("Periodic cleanup completed")

        except asyncio.CancelledError:
            logger.info("Periodic cleanup task cancelled")
            break
        except Exception as e:
            logger.error(f"Error during periodic cleanup: {e}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting up application...")
    container = get_service_container()
    await container.startup()

    # Start periodic cleanup task
    cleanup_task = asyncio.create_task(periodic_cleanup())
    logger.info("Periodic cleanup task started")

    yield

    # Shutdown
    logger.info("Shutting down application...")
    # Cancel and wait for background cleanup task
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        logger.info("Periodic cleanup task cancelled")

    await container.shutdown()


app = FastAPI(lifespan=lifespan)


@app.middleware("http")
async def monitor_memory(request: Request, call_next):
    """Monitor memory usage for each request and log before/after values."""
    # Skip health check endpoint to reduce noise
    if request.url.path == "/health":
        return await call_next(request)

    process = psutil.Process(os.getpid())
    mem_before = process.memory_info().rss / 1024 / 1024  # MB

    logger.info(
        f"Memory before request: {mem_before:.2f}MB for {request.method} {request.url.path}"
    )

    response = await call_next(request)

    mem_after = process.memory_info().rss / 1024 / 1024  # MB
    mem_diff = mem_after - mem_before

    logger.info(
        f"Memory after request: {mem_after:.2f}MB (diff: {mem_diff:+.2f}MB) "
        f"for {request.method} {request.url.path}"
    )

    return response


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

environment = os.getenv("APP_ENVIRONMENT", "dev")

app.include_router(health_router)
app.include_router(parse_router)
app.include_router(parse_router_v2)
app.include_router(generate_router_v2)


if __name__ == "__main__":
    app_host = os.getenv("APP_HOST", "0.0.0.0")
    app_port = int(os.getenv("APP_PORT", "8080"))
    reload = True if environment == "dev" else False

    uvicorn.run(
        app="main:app",
        host=app_host,
        port=app_port,
        log_config=LOGGING_CONFIG,
        reload=reload,
    )
