# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- Install dependencies: `uv sync`
- Install development dependencies: `uv sync --dev`
- Add new dependency: `uv add <package>`
- Run python script: `uv run python <script.py>` avoid run `python`


### Code Quality
- Format and lint code: `ruff check --fix` and `ruff format`
- Run linting only: `ruff check`
- Run tests: `pytest`

### Running the Application
- Development server: `python main.py` (includes auto-reload in dev environment)
- Production server: `uvicorn main:app --host 0.0.0.0 --port 8080`
- Environment variables are loaded from `.env` file

## Architecture Overview

This is a FastAPI-based job posting AI service that uses LangGraph workflows for processing job-related data. The architecture follows a modular design with clear separation of concerns.

### Core Components

1. **LangGraph Workflows** (`app/core/job_post/`):
   - `generate_v2/`: Job post generation workflow with nodes for validation, suggestion, and generation
   - `parse/`: Job post parsing workflow with content validation and information extraction
   - `suggest_*/`: Specialized workflows for suggesting content, skills, and job functions
   - Each workflow has its own `graph.py`, `state.py`, and `nodes/` directory

2. **API Layer** (`app/api/routers/`):
   - Health check endpoint
   - V1 and V2 job post endpoints
   - Follows FastAPI router pattern with versioned APIs

3. **Business Logic** (`app/handlers/`):
   - Orchestrates core workflows
   - Handles input validation and response formatting

4. **Helper Services** (`app/helpers/`):
   - Content extraction from URLs, Google Docs, and files
   - Location and salary suggestion services
   - Metadata processing utilities

5. **Infrastructure** (`app/services/`):
   - Redis caching
   - LangFuse observability
   - Memory monitoring and cleanup

### Key Patterns

- **State Management**: Each LangGraph workflow uses typed state classes for data flow
- **Error Handling**: Custom exceptions in `app/exceptions/` with structured error responses
- **Caching**: Redis-based caching with decorators in `app/utils/cache_decorators.py`
- **Resource Cleanup**: Comprehensive cleanup on application shutdown to prevent memory leaks
- **Observability**: LangFuse integration for LLM call tracking and Sentry for error monitoring

### Dependencies

- **LangChain/LangGraph**: Core AI workflow orchestration
- **FastAPI**: Web framework
- **Redis**: Caching layer
- **Crawl4AI**: Web content extraction
- **OpenAI**: LLM provider via langchain-openai

### Testing

Tests are located in the `tests/` directory. Use `pytest` to run the test suite.

### Environment Setup

The application expects environment variables for:
- OpenAI API configuration
- Redis connection
- Sentry DSN for error tracking
- LangFuse configuration for observability
- Application host/port settings

### Memory Management

The application includes comprehensive memory monitoring and cleanup:
- Automatic resource cleanup on shutdown
- Memory usage logging
- Garbage collection optimization
- Crawler and LLM client cleanup