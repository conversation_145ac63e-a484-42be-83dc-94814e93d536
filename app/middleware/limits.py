from __future__ import annotations

import logging
from typing import Callable

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Response, status
from fastapi.responses import JSONResponse

from app.config import AppSettings

logger = logging.getLogger(__name__)


class RequestLimitsMiddleware:
    """Middleware to enforce request limits based on configuration."""
    
    def __init__(self, settings: AppSettings):
        self.settings = settings
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        """Process request with limits enforcement."""
        
        # Check content length limit
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.settings.request.max_body_size:
                    logger.warning(f"Request body too large: {size} bytes")
                    return JSONResponse(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        content={"error": "Request body too large"}
                    )
            except ValueError:
                pass
        
        # Process request with timeout
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            logger.error(f"Request processing error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error"
            )


def create_request_limits_middleware(settings: AppSettings) -> RequestLimitsMiddleware:
    """Factory function to create request limits middleware."""
    return RequestLimitsMiddleware(settings)
