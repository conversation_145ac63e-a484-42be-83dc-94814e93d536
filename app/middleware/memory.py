from __future__ import annotations

import gc
import logging
import os
from typing import Callable

import psutil
from fastapi import Request, Response

from app.config import AppSettings

logger = logging.getLogger(__name__)


class MemoryMonitoringMiddleware:
    """Middleware for memory monitoring with configuration-based limits."""
    
    def __init__(self, settings: AppSettings):
        self.settings = settings
        self._process = psutil.Process(os.getpid())
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        """Monitor memory usage around request processing."""
        
        # Skip health checks to reduce noise
        if request.url.path == "/health":
            return await call_next(request)
        
        # Get memory before request
        mem_before = self._get_memory_usage()
        logger.debug(f"Memory before request: {mem_before:.2f}MB for {request.method} {request.url.path}")
        
        response = await call_next(request)
        
        # Get memory after request
        mem_after = self._get_memory_usage()
        mem_diff = mem_after - mem_before
        
        logger.debug(f"Memory after request: {mem_after:.2f}MB (diff: {mem_diff:+.2f}MB)")
        
        # Trigger cleanup if memory is high
        if mem_after > self.settings.memory.warning_threshold_mb:
            await self._handle_high_memory(mem_after)
        
        return response
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        return self._process.memory_info().rss / 1024 / 1024
    
    async def _handle_high_memory(self, current_memory: float) -> None:
        """Handle high memory usage with configured cleanup."""
        logger.warning(f"High memory usage detected: {current_memory:.2f}MB")
        
        # Perform garbage collection
        gc_passes = (self.settings.memory.gc_passes_critical 
                    if current_memory > self.settings.memory.critical_threshold_mb 
                    else self.settings.memory.gc_passes_normal)
        
        collected = 0
        for _ in range(gc_passes):
            collected += gc.collect()
        
        logger.info(f"Garbage collection freed {collected} objects")
        
        # Log final memory usage
        final_memory = self._get_memory_usage()
        freed = current_memory - final_memory
        logger.info(f"Memory after cleanup: {final_memory:.2f}MB (freed: {freed:.2f}MB)")


def create_memory_monitoring_middleware(settings: AppSettings) -> MemoryMonitoringMiddleware:
    """Factory function to create memory monitoring middleware."""
    return MemoryMonitoringMiddleware(settings)
