from __future__ import annotations

import os
from dataclasses import dataclass
from functools import lru_cache
from typing import Optional


@dataclass(frozen=True, kw_only=True)
class RedisSettings:
    url: str = "redis://localhost:6379"
    max_connections: int = 20
    min_connections: int = 5
    connection_timeout: int = 10
    socket_timeout: int = 5
    health_check_interval: int = 30
    default_ttl: int = 30 * 24 * 60 * 60  # 30 days


@dataclass(frozen=True, kw_only=True)
class CacheSettings:
    max_size: int = 1000
    default_ttl: int = 3600
    cleanup_interval: int = 300


@dataclass(frozen=True, kw_only=True)
class HTTPSettings:
    timeout: int = 30
    max_connections: int = 100
    max_keepalive_connections: int = 20
    keepalive_expiry: int = 5


@dataclass(frozen=True, kw_only=True)
class BrowserSettings:
    pool_size: int = 3
    page_timeout: int = 30000
    max_concurrent: int = 5
    cleanup_interval: int = 300


@dataclass(frozen=True, kw_only=True)
class FileSettings:
    max_file_size: int = 5 * 1024 * 1024  # 5MB
    allowed_extensions: tuple = (".pdf", ".docx", ".txt")
    temp_dir: str = "/tmp/upzi"
    cleanup_interval: int = 3600


@dataclass(frozen=True, kw_only=True)
class MemorySettings:
    warning_threshold_mb: int = 500
    critical_threshold_mb: int = 1000
    cleanup_interval: int = 15
    gc_passes_normal: int = 3
    gc_passes_critical: int = 5


@dataclass(frozen=True, kw_only=True)
class RequestSettings:
    max_body_size: int = 10 * 1024 * 1024  # 10MB
    timeout: int = 120
    max_concurrent: int = 50


@dataclass(frozen=True, kw_only=True)
class WorkflowSettings:
    max_graph_instances: int = 5
    graph_cache_ttl: int = 3600
    max_concurrent_workflows: int = 10
    node_timeout: int = 30
    max_content_length: int = 50000
    enum_cache_ttl: int = 300
    model_cache_maxsize: int = 100


@dataclass(frozen=True, kw_only=True)
class LLMSettings:
    max_instances: int = 10
    default_model: str = "gpt-4o-mini"
    default_temperature: float = 0.0
    timeout: int = 60
    max_tokens: int = 4000


@dataclass(frozen=True, kw_only=True)
class AppSettings:
    environment: str = "dev"
    host: str = "0.0.0.0"
    port: int = 8080
    debug: bool = True
    
    # Service configurations
    redis: RedisSettings
    cache: CacheSettings
    http: HTTPSettings
    browser: BrowserSettings
    file: FileSettings
    memory: MemorySettings
    request: RequestSettings
    workflow: WorkflowSettings
    llm: LLMSettings
    
    # External API settings
    openai_api_key: Optional[str] = None
    sentry_dsn: Optional[str] = None
    langfuse_secret_key: Optional[str] = None
    langfuse_public_key: Optional[str] = None


@lru_cache(maxsize=1)
def get_settings() -> AppSettings:
    """Get application settings from environment variables."""
    return AppSettings(
        environment=os.getenv("APP_ENVIRONMENT", "dev"),
        host=os.getenv("APP_HOST", "0.0.0.0"),
        port=int(os.getenv("APP_PORT", "8080")),
        debug=os.getenv("APP_ENVIRONMENT", "dev") == "dev",
        
        redis=RedisSettings(
            url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "20")),
            min_connections=int(os.getenv("REDIS_MIN_CONNECTIONS", "5")),
            connection_timeout=int(os.getenv("REDIS_CONNECTION_TIMEOUT", "10")),
            socket_timeout=int(os.getenv("REDIS_SOCKET_TIMEOUT", "5")),
            health_check_interval=int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30")),
        ),
        
        cache=CacheSettings(
            max_size=int(os.getenv("CACHE_MAX_SIZE", "1000")),
            default_ttl=int(os.getenv("CACHE_DEFAULT_TTL", "3600")),
            cleanup_interval=int(os.getenv("CACHE_CLEANUP_INTERVAL", "300")),
        ),
        
        http=HTTPSettings(
            timeout=int(os.getenv("HTTP_TIMEOUT", "30")),
            max_connections=int(os.getenv("HTTP_MAX_CONNECTIONS", "100")),
            max_keepalive_connections=int(os.getenv("HTTP_MAX_KEEPALIVE_CONNECTIONS", "20")),
            keepalive_expiry=int(os.getenv("HTTP_KEEPALIVE_EXPIRY", "5")),
        ),
        
        browser=BrowserSettings(
            pool_size=int(os.getenv("BROWSER_POOL_SIZE", "3")),
            page_timeout=int(os.getenv("BROWSER_PAGE_TIMEOUT", "30000")),
            max_concurrent=int(os.getenv("BROWSER_MAX_CONCURRENT", "5")),
            cleanup_interval=int(os.getenv("BROWSER_CLEANUP_INTERVAL", "300")),
        ),
        
        file=FileSettings(
            max_file_size=int(os.getenv("FILE_MAX_SIZE", str(5 * 1024 * 1024))),
            temp_dir=os.getenv("FILE_TEMP_DIR", "/tmp/upzi"),
            cleanup_interval=int(os.getenv("FILE_CLEANUP_INTERVAL", "3600")),
        ),
        
        memory=MemorySettings(
            warning_threshold_mb=int(os.getenv("MEMORY_WARNING_THRESHOLD_MB", "500")),
            critical_threshold_mb=int(os.getenv("MEMORY_CRITICAL_THRESHOLD_MB", "1000")),
            cleanup_interval=int(os.getenv("MEMORY_CLEANUP_INTERVAL", "15")),
            gc_passes_normal=int(os.getenv("MEMORY_GC_PASSES_NORMAL", "3")),
            gc_passes_critical=int(os.getenv("MEMORY_GC_PASSES_CRITICAL", "5")),
        ),
        
        request=RequestSettings(
            max_body_size=int(os.getenv("REQUEST_MAX_BODY_SIZE", str(10 * 1024 * 1024))),
            timeout=int(os.getenv("REQUEST_TIMEOUT", "120")),
            max_concurrent=int(os.getenv("REQUEST_MAX_CONCURRENT", "50")),
        ),
        
        # External services
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        sentry_dsn=os.getenv("SENTRY_DSN"),
        langfuse_secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
        langfuse_public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    )
