from __future__ import annotations

from functools import lru_cache
from typing import Annotated

from fastapi import Depends
from langfuse.callback import Callback<PERSON>andler

from app.config import AppSettings, get_settings
from app.core.llm import LLMService
from app.managers import (
    <PERSON><PERSON>er<PERSON>ana<PERSON>,
    DatabaseManager,
    FileManager,
    HTTPManager,
)
from app.services.cache import CacheService


# Settings dependency
def get_app_settings() -> AppSettings:
    """Get application settings."""
    return get_settings()


SettingsDep = Annotated[AppSettings, Depends(get_app_settings)]


# Manager dependencies
@lru_cache(maxsize=1)
def get_database_manager(settings: SettingsDep) -> DatabaseManager:
    """Get database manager instance."""
    return DatabaseManager(settings)


@lru_cache(maxsize=1)
def get_file_manager(settings: SettingsDep) -> FileManager:
    """Get file manager instance."""
    return FileManager(settings)


@lru_cache(maxsize=1)
def get_browser_manager(settings: SettingsDep) -> BrowserManager:
    """Get browser manager instance."""
    return BrowserManager(settings)


@lru_cache(maxsize=1)
def get_http_manager(settings: SettingsDep) -> HTTPManager:
    """Get HTTP manager instance."""
    return HTTPManager(settings)


DatabaseManagerDep = Annotated[DatabaseManager, Depends(get_database_manager)]
FileManagerDep = Annotated[FileManager, Depends(get_file_manager)]
BrowserManagerDep = Annotated[BrowserManager, Depends(get_browser_manager)]
HTTPManagerDep = Annotated[HTTPManager, Depends(get_http_manager)]


# Service dependencies
@lru_cache(maxsize=1)
def get_cache_service(db_manager: DatabaseManagerDep, settings: SettingsDep) -> CacheService:
    """Get cache service instance."""
    return CacheService(db_manager, settings)


@lru_cache(maxsize=1)
def get_llm_service(settings: SettingsDep) -> LLMService:
    """Get LLM service instance."""
    return LLMService(settings)


@lru_cache(maxsize=1)
def get_langfuse_handler(settings: SettingsDep) -> CallbackHandler:
    """Get LangFuse handler instance."""
    return CallbackHandler(
        secret_key=settings.langfuse_secret_key,
        public_key=settings.langfuse_public_key,
    )


CacheServiceDep = Annotated[CacheService, Depends(get_cache_service)]
LLMServiceDep = Annotated[LLMService, Depends(get_llm_service)]
LangFuseHandlerDep = Annotated[CallbackHandler, Depends(get_langfuse_handler)]
