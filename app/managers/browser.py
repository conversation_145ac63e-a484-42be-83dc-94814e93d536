from __future__ import annotations

import asyncio

from crawl4ai import Async<PERSON>ebCrawler

from app.config import AppSettings
from app.managers.base import BaseManager


class BrowserManager(BaseManager):
    """Context manager for browser instances with pool management."""
    
    def __init__(self, settings: AppSettings):
        super().__init__("Browser")
        self.settings = settings
        self._semaphore = asyncio.Semaphore(self.settings.browser.max_concurrent)
    
    async def _create_resource(self, **kwargs) -> AsyncWebCrawler:
        """Create browser crawler instance."""
        # Acquire semaphore to limit concurrent browsers
        await self._semaphore.acquire()
        
        try:
            crawler = AsyncWebCrawler(
                verbose=False,
                headless=True,
                page_timeout=self.settings.browser.page_timeout,
                **kwargs
            )
            
            await crawler.__aenter__()
            self.logger.debug("Browser crawler initialized")
            return crawler
            
        except Exception as e:
            self._semaphore.release()
            self.logger.error(f"Failed to create browser crawler: {e}")
            raise
    
    async def _cleanup_resource(self, resource: AsyncWebCrawler) -> None:
        """Clean up browser crawler."""
        try:
            await resource.__aexit__(None, None, None)
            self.logger.debug("Browser crawler closed")
        except Exception as e:
            self.logger.error(f"Error closing browser crawler: {e}")
        finally:
            self._semaphore.release()
