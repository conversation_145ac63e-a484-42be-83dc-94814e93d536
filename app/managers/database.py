from __future__ import annotations

import redis.asyncio as aioredis
from redis.asyncio import ConnectionPool

from app.config import AppSettings
from app.managers.base import BaseManager


class DatabaseManager(BaseManager):
    """Context manager for Redis database connections."""
    
    def __init__(self, settings: AppSettings):
        super().__init__("Database")
        self.settings = settings
        self._pool: ConnectionPool | None = None
    
    async def _create_resource(self, **kwargs) -> aioredis.Redis:
        """Create Redis connection from pool."""
        if not self._pool:
            self._pool = ConnectionPool.from_url(
                self.settings.redis.url,
                max_connections=self.settings.redis.max_connections,
                socket_timeout=self.settings.redis.socket_timeout,
                socket_connect_timeout=self.settings.redis.connection_timeout,
                health_check_interval=self.settings.redis.health_check_interval,
            )
        
        redis_client = aioredis.Redis(connection_pool=self._pool)
        
        # Test connection
        await redis_client.ping()
        self.logger.debug("Redis connection established")
        
        return redis_client
    
    async def _cleanup_resource(self, resource: aioredis.Redis) -> None:
        """Clean up Redis connection."""
        try:
            await resource.aclose()
            self.logger.debug("Redis connection closed")
        except Exception as e:
            self.logger.error(f"Error closing Redis connection: {e}")
    
    async def close_pool(self) -> None:
        """Close the connection pool."""
        if self._pool:
            try:
                await self._pool.aclose()
                self._pool = None
                self.logger.info("Redis connection pool closed")
            except Exception as e:
                self.logger.error(f"Error closing Redis pool: {e}")
