from __future__ import annotations

import asyncio
import os
import tempfile
from pathlib import Path

import aiofiles

from app.config import AppSettings
from app.managers.base import BaseManager


class FileHandle:
    """Managed file handle with automatic cleanup."""
    
    def __init__(self, file_path: Path, temp_file: bool = False):
        self.path = file_path
        self.temp_file = temp_file
        self._handle: aiofiles.threadpool.text.AsyncTextIOWrapper | None = None
    
    async def open(self, mode: str = "r") -> aiofiles.threadpool.text.AsyncTextIOWrapper:
        """Open the file handle."""
        self._handle = await aiofiles.open(self.path, mode=mode)
        return self._handle
    
    async def close(self) -> None:
        """Close file handle and clean up temp file."""
        if self._handle:
            await self._handle.close()
            self._handle = None
        
        if self.temp_file and self.path.exists():
            try:
                self.path.unlink()
            except Exception:
                pass


class FileManager(BaseManager):
    """Context manager for file operations with automatic cleanup."""
    
    def __init__(self, settings: AppSettings):
        super().__init__("File")
        self.settings = settings
        self._temp_files: set[Path] = set()
        self._cleanup_task: asyncio.Task | None = None
        
        # Ensure temp directory exists
        os.makedirs(self.settings.file.temp_dir, exist_ok=True)
    
    async def _create_resource(self, content: bytes | str | None = None, 
                             suffix: str = "", **kwargs) -> FileHandle:
        """Create a temporary file resource."""
        temp_dir = Path(self.settings.file.temp_dir)
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(
            dir=temp_dir,
            suffix=suffix,
            delete=False
        )
        temp_path = Path(temp_file.name)
        temp_file.close()
        
        # Write content if provided
        if content is not None:
            mode = "wb" if isinstance(content, bytes) else "w"
            async with aiofiles.open(temp_path, mode=mode) as f:
                await f.write(content)
        
        file_handle = FileHandle(temp_path, temp_file=True)
        self._temp_files.add(temp_path)
        
        self.logger.debug(f"Created temporary file: {temp_path}")
        return file_handle
    
    async def _cleanup_resource(self, resource: FileHandle) -> None:
        """Clean up file resource."""
        try:
            await resource.close()
            if resource.path in self._temp_files:
                self._temp_files.remove(resource.path)
        except Exception as e:
            self.logger.error(f"Error cleaning up file resource: {e}")
    
    async def cleanup_old_files(self) -> None:
        """Clean up old temporary files."""
        temp_dir = Path(self.settings.file.temp_dir)
        if not temp_dir.exists():
            return
        
        try:
            for file_path in temp_dir.glob("*"):
                if file_path.is_file():
                    # Remove files older than cleanup interval
                    age = asyncio.get_event_loop().time() - file_path.stat().st_mtime
                    if age > self.settings.file.cleanup_interval:
                        file_path.unlink()
                        self.logger.debug(f"Cleaned up old file: {file_path}")
        except Exception as e:
            self.logger.error(f"Error during file cleanup: {e}")
    
    def start_cleanup_task(self) -> None:
        """Start periodic cleanup task."""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def stop_cleanup_task(self) -> None:
        """Stop periodic cleanup task."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
    
    async def _periodic_cleanup(self) -> None:
        """Periodic cleanup of old files."""
        while True:
            try:
                await asyncio.sleep(self.settings.file.cleanup_interval)
                await self.cleanup_old_files()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in periodic file cleanup: {e}")
