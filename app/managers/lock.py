from __future__ import annotations


import redis.asyncio as aioredis

from app.managers.base import BaseManager


class DistributedLock:
    """Distributed lock using Redis."""
    
    def __init__(self, redis_client: aioredis.Redis, key: str, timeout: int = 60):
        self.redis_client = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self._acquired = False
    
    async def acquire(self) -> bool:
        """Acquire the distributed lock."""
        result = await self.redis_client.set(
            self.key, "locked", 
            nx=True, 
            ex=self.timeout
        )
        self._acquired = bool(result)
        return self._acquired
    
    async def release(self) -> None:
        """Release the distributed lock."""
        if self._acquired:
            await self.redis_client.delete(self.key)
            self._acquired = False


class LockManager(BaseManager):
    """Context manager for distributed locks."""
    
    def __init__(self, redis_client: aioredis.Redis):
        super().__init__("Lock")
        self.redis_client = redis_client
    
    async def _create_resource(self, key: str, timeout: int = 60, **kwargs) -> DistributedLock:
        """Create and acquire a distributed lock."""
        lock = DistributedLock(self.redis_client, key, timeout)
        
        acquired = await lock.acquire()
        if not acquired:
            raise RuntimeError(f"Failed to acquire lock for key: {key}")
        
        self.logger.debug(f"Acquired distributed lock: {key}")
        return lock
    
    async def _cleanup_resource(self, resource: DistributedLock) -> None:
        """Release the distributed lock."""
        try:
            await resource.release()
            self.logger.debug("Released distributed lock")
        except Exception as e:
            self.logger.error(f"Error releasing lock: {e}")
