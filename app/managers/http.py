from __future__ import annotations

import httpx

from app.config import AppSettings
from app.managers.base import BaseManager


class HTTPManager(BaseManager):
    """Context manager for HTTP client sessions."""
    
    def __init__(self, settings: AppSettings):
        super().__init__("HTTP")
        self.settings = settings
    
    async def _create_resource(self, **kwargs) -> httpx.AsyncClient:
        """Create HTTP client with configured limits."""
        limits = httpx.Limits(
            max_connections=self.settings.http.max_connections,
            max_keepalive_connections=self.settings.http.max_keepalive_connections,
            keepalive_expiry=self.settings.http.keepalive_expiry,
        )
        
        timeout = httpx.Timeout(self.settings.http.timeout)
        
        client = httpx.AsyncClient(
            limits=limits,
            timeout=timeout,
            **kwargs
        )
        
        self.logger.debug("HTTP client created")
        return client
    
    async def _cleanup_resource(self, resource: httpx.AsyncClient) -> None:
        """Clean up HTTP client."""
        try:
            await resource.aclose()
            self.logger.debug("HTTP client closed")
        except Exception as e:
            self.logger.error(f"Error closing HTTP client: {e}")
