from __future__ import annotations

import logging
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Generic, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar("T")


class ManagedResource(Generic[T]):
    """Wrapper for managed resources with automatic cleanup."""
    
    def __init__(self, resource: T, cleanup_func: callable | None = None):
        self.resource = resource
        self._cleanup_func = cleanup_func
        self._is_cleaned = False
    
    async def cleanup(self) -> None:
        """Clean up the managed resource."""
        if not self._is_cleaned and self._cleanup_func:
            try:
                if hasattr(self._cleanup_func, "__call__"):
                    result = self._cleanup_func(self.resource)
                    if hasattr(result, "__await__"):
                        await result
                self._is_cleaned = True
            except Exception as e:
                logger.error(f"Error during resource cleanup: {e}")
    
    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the wrapped resource."""
        return getattr(self.resource, name)


class BaseManager(ABC):
    """Base class for all resource managers."""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @asynccontextmanager
    async def acquire(self, **kwargs) -> AsyncGenerator[ManagedResource[T], None]:
        """Acquire a managed resource with automatic cleanup."""
        resource = None
        try:
            self.logger.debug(f"Acquiring {self.name} resource")
            resource = await self._create_resource(**kwargs)
            managed = ManagedResource(resource, self._cleanup_resource)
            yield managed
        except Exception as e:
            self.logger.error(f"Error acquiring {self.name} resource: {e}")
            if resource:
                await self._cleanup_resource(resource)
            raise
        finally:
            if resource:
                await self._cleanup_resource(resource)
                self.logger.debug(f"Released {self.name} resource")
    
    @abstractmethod
    async def _create_resource(self, **kwargs) -> T:
        """Create the resource instance."""
        pass
    
    async def _cleanup_resource(self, resource: T) -> None:
        """Clean up the resource. Override if needed."""
        if hasattr(resource, "close"):
            result = resource.close()
            if hasattr(result, "__await__"):
                await result
