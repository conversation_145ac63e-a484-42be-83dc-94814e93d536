from sentry_sdk import capture_exception

from app.container import get_service_container
from app.core.job_post import GenerateJobPostInputV2
from app.exceptions import GenerateError
from app.helpers import get_meta_job_post
from app.helpers.get_location import get_city, get_country
from app.helpers.hash_dictionary import hash_dictionary
from app.services.cache import CacheService


async def job_post_generate_v2(
    data: GenerateJobPostInputV2,
    graph,
    cache_service: CacheService,
):
    try:
        container = get_service_container()
        langfuse_handler = container.langfuse_handler
        # Create cache key based on input data
        cache_data = {
            "job_title": data.job_title,
            "job_details": data.job_details,
            "language": data.language,
            "company_information": data.company_information,
        }
        cache_key = f"job_post_generate_v2_{hash_dictionary(cache_data)}"

        # Try to get cached response
        cached_response = await cache_service.get(cache_key)
        if cached_response:
            return cached_response.get("error"), cached_response.get("response")

        # If not cached, generate new response
        metadata = await get_meta_job_post()
        country_list = await get_country()
        country_id = country_list[0]["id"]
        city = await get_city(country_id)
        if metadata:
            metadata.update({"city": city})
            data.metadata = metadata
        response = await graph.ainvoke(
            data,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Job Generate V2",
            },
        )
        if not response:
            return None, None

        # Prepare response data
        error = None
        response_data = None
        if response.get("is_suggested"):
            error = response.get("job_title_suggested")
            response_data = {"parsed": response["job_post_sections"]}
        else:
            response_data = {"parsed": response["job_post_sections"]}

        # Cache the response (fire and forget - don't fail if caching fails)
        cache_response = {"error": error, "response": response_data}
        await cache_service.set(cache_key, cache_response)

        return error, response_data
    except Exception as e:
        capture_exception(e)
        raise GenerateError(str(e))
