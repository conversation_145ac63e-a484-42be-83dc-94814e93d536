import logging

from sentry_sdk import capture_exception

from app.container import get_service_container
from app.core.job_post import ParseJobPostInput
from app.exceptions import ParseError
from app.helpers import get_city, get_country, get_meta_job_post, hash_dictionary
from app.services.cache import CacheService

logger = logging.getLogger(__name__)


async def job_post_parse(
    data: ParseJobPostInput,
    graph,
    cache_service: CacheService,
):
    try:
        container = get_service_container()
        langfuse_handler = container.langfuse_handler
        # Create cache key based on input data
        cache_data = {
            "content": data.content,
            "metadata": data.metadata,
        }
        cache_key = f"job_post_parse_{hash_dictionary(cache_data)}"

        # Try to get cached response
        cached_response = await cache_service.get(cache_key)
        if cached_response:
            return cached_response

        # If not cached, generate new response
        metadata = await get_meta_job_post()
        country_list = await get_country()
        if country_list and len(country_list) > 0:
            country_id = country_list[0]["id"]
            city = await get_city(country_id)
            if metadata and city:
                metadata.update({"city": city})
                data.metadata = metadata
        elif metadata:
            data.metadata = metadata

        response = await graph.ainvoke(
            data,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Job Parser",
            },
        )

        if not response:
            return None

        result = {
            "parsed": response["job_post_parsed"],
            "suggest": response["job_post_suggest"],
        }

        # Cache the response (fire and forget - don't fail if caching fails)
        await cache_service.set(cache_key, result)

        return result
    except Exception as e:
        capture_exception(e)
        raise ParseError(str(e))
    finally:
        pass


async def job_post_parse_v2(
    data: ParseJobPostInput,
    graph,
    cache_service: CacheService,
):
    try:
        container = get_service_container()
        langfuse_handler = container.langfuse_handler
        # Create cache key based on input data
        cache_data = {
            "content": data.content,
            "metadata": data.metadata,
        }
        cache_key = f"job_post_parse_v2_{hash_dictionary(cache_data)}"

        # Try to get cached response
        cached_response = await cache_service.get(cache_key)
        if cached_response:
            return cached_response

        # If not cached, generate new response
        metadata = await get_meta_job_post()
        country_list = await get_country()
        if country_list and len(country_list) > 0:
            country_id = country_list[0]["id"]
            city = await get_city(country_id)
            if metadata and city:
                metadata.update({"city": city})
                data.metadata = metadata
        elif metadata:
            data.metadata = metadata

        response = await graph.ainvoke(
            data,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Job Parser V2",
            },
        )

        if not response:
            return None

        result = {"parsed": response["job_post_parsed"]}

        # Cache the response (fire and forget - don't fail if caching fails)
        await cache_service.set(cache_key, result)

        return result
    except Exception as e:
        capture_exception(e)
        raise ParseError(str(e))
    finally:
        pass
