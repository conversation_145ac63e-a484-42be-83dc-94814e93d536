"""
New parse v2 handler using workflow context managers and dependency injection.

This handler demonstrates the new architecture with proper resource management,
dependency injection, and context managers.
"""

import logging
from typing import Any, Dict, Optional

from sentry_sdk import capture_exception

from app.core.job_post import ParseJobPostInput
from app.core.job_post.factory import GraphFactory, WorkflowType
from app.core.job_post.factory.workflow_config import WorkflowSettings
from app.core.job_post.managers import WorkflowContext
from app.core.job_post.parse.state import (
    ParseJobPostOutput,
    ParseJobPostState,
)
from app.core.job_post.services import UnifiedCacheService
from app.exceptions import ParseError
from app.helpers import get_city, get_country, get_meta_job_post, hash_dictionary

logger = logging.getLogger(__name__)


async def job_post_parse_v2_new(
    data: ParseJobPostInput,
    graph_factory: GraphFactory,
    cache_service: UnifiedCacheService,
    settings: WorkflowSettings,
    langfuse_handler: Optional[Any] = None,
) -> Optional[Dict[str, Any]]:
    """
    Parse job post using the new workflow architecture.

    Args:
        data: Input data for parsing
        graph_factory: Graph factory with dependency injection
        cache_service: Unified cache service
        settings: Workflow settings
        langfuse_handler: Optional LangFuse handler for tracing

    Returns:
        Parsed job post data or None if parsing failed
    """
    # Create cache key based on input data
    cache_data = {
        "content": data.content,
        "metadata": data.metadata,
    }
    cache_key = f"job_post_parse_v2_{hash_dictionary(cache_data)}"

    try:
        # Try to get cached response first
        cached_response = await cache_service.get("parse_results", cache_key)
        if cached_response:
            logger.info(f"Cache hit for parse v2: {cache_key}")
            return cached_response

        # Use workflow context for resource management
        async with WorkflowContext(
            WorkflowType.PARSE_V2,
            graph_factory,
            settings,
            workflow_id=f"parse_v2_{cache_key[:8]}",
        ) as workflow_ctx:
            # Prepare metadata
            metadata = await _prepare_metadata()
            if metadata:
                data.metadata = metadata

            # Execute the workflow
            graph_input = {
                "content": data.content,
                "metadata": data.metadata,
                "is_valid_content": True,  # Will be validated by the workflow
            }

            # Configure execution
            config = {}
            if langfuse_handler:
                config["callbacks"] = [langfuse_handler]
                config["run_name"] = "Job Parser V2"

            # Execute workflow with context management
            response = await workflow_ctx.execute_workflow(
                graph_input, ParseJobPostState, ParseJobPostInput, ParseJobPostOutput
            )

            if not response:
                logger.warning("Parse v2 workflow returned empty response")
                return None

            # Format result
            result = {"parsed": response.get("job_post_parsed")}

            # Cache the response (fire and forget - don't fail if caching fails)
            try:
                await cache_service.set("parse_results", cache_key, result)
                logger.debug(f"Cached parse v2 result: {cache_key}")
            except Exception as cache_error:
                logger.warning(f"Failed to cache parse v2 result: {cache_error}")

            # Log execution stats
            stats = workflow_ctx.get_execution_stats()
            logger.info(f"Parse v2 completed: {stats}")

            return result

    except Exception as e:
        capture_exception(e)
        logger.error(f"Parse v2 failed: {e}")
        raise ParseError(str(e))


async def _prepare_metadata() -> Optional[Dict[str, Any]]:
    """
    Prepare metadata for the parsing workflow.

    Returns:
        Metadata dictionary with job post information
    """
    try:
        # Get metadata from external services
        metadata = await get_meta_job_post()
        country_list = await get_country()

        if country_list and len(country_list) > 0:
            country_id = country_list[0]["id"]
            city = await get_city(country_id)
            if metadata and city:
                metadata.update({"city": city})
                return metadata

        return metadata

    except Exception as e:
        logger.warning(f"Failed to prepare metadata: {e}")
        return None


# Compatibility wrapper for existing code
async def job_post_parse_v2_wrapper(
    data: ParseJobPostInput,
    graph_factory: GraphFactory,
    cache_service: UnifiedCacheService,
    settings: WorkflowSettings,
    langfuse_handler: Optional[Any] = None,
) -> Optional[Dict[str, Any]]:
    """
    Wrapper function that maintains compatibility with existing interfaces.

    This function can be used as a drop-in replacement for the old handler
    while providing the new architecture benefits.
    """
    return await job_post_parse_v2_new(
        data=data,
        graph_factory=graph_factory,
        cache_service=cache_service,
        settings=settings,
        langfuse_handler=langfuse_handler,
    )
