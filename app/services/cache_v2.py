from __future__ import annotations

import json
import logging
from typing import Any

from app.config import AppSettings
from app.managers import DatabaseManager

logger = logging.getLogger(__name__)


class CacheService:
    """Refactored cache service using context managers."""
    
    def __init__(self, db_manager: DatabaseManager, settings: AppSettings):
        self.db_manager = db_manager
        self.settings = settings
        self.default_ttl = settings.cache.default_ttl
    
    async def get(self, key: str) -> Any | None:
        """Get value from cache with automatic connection management."""
        try:
            async with self.db_manager.acquire() as redis:
                cached_data = await redis.get(key)
                if cached_data:
                    if isinstance(cached_data, bytes):
                        cached_data = cached_data.decode("utf-8")
                    return json.loads(cached_data)
                return None
        except Exception as e:
            logger.warning(f"Cache get failed for key '{key}': {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int | None = None) -> bool:
        """Set value in cache with automatic connection management."""
        try:
            ttl = ttl or self.default_ttl
            json_data = json.dumps(value, separators=(",", ":"), ensure_ascii=False)
            
            async with self.db_manager.acquire() as redis:
                await redis.set(key, json_data, ex=ttl)
                return True
        except Exception as e:
            logger.warning(f"Cache set failed for key '{key}': {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache with automatic connection management."""
        try:
            async with self.db_manager.acquire() as redis:
                await redis.delete(key)
                return True
        except Exception as e:
            logger.warning(f"Cache delete failed for key '{key}': {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            async with self.db_manager.acquire() as redis:
                result = await redis.exists(key)
                return bool(result)
        except Exception as e:
            logger.warning(f"Cache exists check failed for key '{key}': {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        try:
            async with self.db_manager.acquire() as redis:
                keys = await redis.keys(pattern)
                if keys:
                    deleted = await redis.delete(*keys)
                    logger.info(f"Cleared {deleted} cache keys matching '{pattern}'")
                    return deleted
                return 0
        except Exception as e:
            logger.error(f"Cache clear pattern failed for '{pattern}': {e}")
            return 0
