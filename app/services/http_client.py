"""
HTTP client service providing a shared httpx.AsyncClient instance.
"""

import logging
from typing import Optional

import httpx

logger = logging.getLogger(__name__)


class HTTPClientService:
    """Service for managing a shared HTTP client instance."""

    def __init__(self):
        self._client: Optional[httpx.AsyncClient] = None

    @property
    def client(self) -> httpx.AsyncClient:
        """Get the shared HTTP client instance (lazy initialization)."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(timeout=30.0),  # Default timeout
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
            )
            logger.debug("HTTP client initialized")
        return self._client

    async def cleanup(self):
        """Close the HTTP client and cleanup resources."""
        if self._client:
            await self._client.aclose()
            self._client = None
            logger.debug("HTTP client closed")
