import asyncio
import logging
import os
from typing import Optional

from redis.asyncio import <PERSON>Pool, Redis
from redis.exceptions import ConnectionError, TimeoutError

logger = logging.getLogger(__name__)


class RedisService:
    """Redis service with proper connection management and memory leak prevention."""

    def __init__(self):
        self._pool: Optional[ConnectionPool] = None
        self._redis: Optional[Redis] = None
        self._lock = asyncio.Lock()

    async def _ensure_connection(self) -> Optional[Redis]:
        """Ensure Redis connection is established with proper error handling."""
        if self._redis is None:
            async with self._lock:
                if self._redis is None:
                    await self._create_connection()
        if self._redis is None:
            raise ConnectionError("Failed to establish Redis connection")
        return self._redis

    async def _create_connection(self):
        """Create Redis connection with optimized pool settings."""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")

            # Create connection pool with memory-optimized settings
            self._pool = ConnectionPool.from_url(
                redis_url,
                max_connections=20,  # Limit max connections
                retry_on_timeout=True,
                retry_on_error=[ConnectionError, TimeoutError],
                health_check_interval=30,  # Health check every 30 seconds
                socket_keepalive=True,
                socket_keepalive_options={},
                decode_responses=False,  # Keep as bytes to avoid encoding overhead
            )

            self._redis = Redis(
                connection_pool=self._pool,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
            )

            # Test connection
            await self._redis.ping()
            logger.info("Redis connection established successfully")

        except Exception:
            await self._cleanup_connection()
            raise

    async def _cleanup_connection(self):
        """Clean up Redis connections to prevent memory leaks."""
        try:
            if self._redis:
                await self._redis.aclose()
                self._redis = None

            if self._pool:
                await self._pool.aclose()
                self._pool = None

        except Exception as e:
            logger.warning(f"Error during Redis cleanup: {e}")

    async def get(self, key: str) -> Optional[bytes]:
        """Get value from Redis with proper error handling."""
        try:
            redis = await self._ensure_connection()
            if redis is None:
                return None
            return await redis.get(key)
        except Exception:
            return None

    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Set value in Redis with proper error handling."""
        try:
            redis = await self._ensure_connection()
            if redis is None:
                return False
            await redis.set(key, value, ex=ex)
            return True
        except Exception:
            return False

    async def delete(self, key: str) -> bool:
        """Delete key from Redis with proper error handling."""
        try:
            redis = await self._ensure_connection()
            if redis is None:
                return False
            await redis.delete(key)
            return True
        except Exception:
            return False

    async def close(self):
        """Close Redis connections gracefully."""
        await self._cleanup_connection()
        logger.info("Redis service closed")


async def cleanup_redis(redis_service: RedisService):
    """Cleanup function for Redis service."""
    await redis_service.close()
    logger.info("Redis cleanup completed.")
