"""
Browser pool service for managing shared AsyncWebCrawler instances to prevent memory leaks.
"""

import asyncio
import logging
import os
from typing import List
import psutil
from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>, BrowserConfig

logger = logging.getLogger(__name__)


class BrowserPoolService:
    """Service for managing a pool of shared AsyncWebCrawler instances."""

    def __init__(self, pool_size: int = 3):
        self.pool_size = pool_size
        self._crawlers: List[AsyncWebCrawler] = []
        self._available_crawlers: asyncio.Queue = asyncio.Queue()
        self._lock = asyncio.Lock()
        self._initialized = False
        self._browser_config = None

    def _create_browser_config(self) -> BrowserConfig:
        """Create browser configuration for crawlers."""
        if self._browser_config is None:
            proxy_server = os.getenv("PROXY_SERVER")
            proxy_username = os.getenv("PROXY_USERNAME")
            proxy_password = os.getenv("PROXY_PASSWORD")

            proxy_config = None
            if proxy_server and proxy_username and proxy_password:
                proxy_config = {
                    "server": proxy_server,
                    "username": proxy_username,
                    "password": proxy_password,
                }

            self._browser_config = BrowserConfig(
                verbose=False,  # Reduce logging noise
                proxy_config=proxy_config,
                java_script_enabled=False,
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.187 Safari/537.36",
                text_mode=True,
                browser_type="chromium",
                headless=True,
            )
        return self._browser_config

    async def _initialize_pool(self):
        """Initialize the browser pool with crawler instances."""
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return

            browser_config = self._create_browser_config()

            for i in range(self.pool_size):
                try:
                    crawler = AsyncWebCrawler(config=browser_config)
                    await crawler.awarmup()  # Initialize browser
                    self._crawlers.append(crawler)
                    await self._available_crawlers.put(crawler)
                    logger.debug(f"Initialized browser {i + 1}/{self.pool_size}")
                except Exception as e:
                    logger.error(f"Failed to initialize browser {i + 1}: {e}")
                    # Continue with fewer browsers if some fail

            self._initialized = True
            logger.info(f"Browser pool initialized with {len(self._crawlers)} browsers")

    async def get_crawler(self) -> AsyncWebCrawler:
        """Get an available crawler from the pool."""
        if not self._initialized:
            await self._initialize_pool()

        # Wait for an available crawler (with timeout to prevent hanging)
        try:
            crawler = await asyncio.wait_for(
                self._available_crawlers.get(), timeout=30.0
            )
            return crawler
        except asyncio.TimeoutError:
            logger.error("Timeout waiting for available browser")
            raise RuntimeError("No available browsers in pool")

    async def return_crawler(self, crawler: AsyncWebCrawler):
        """Return a crawler to the pool for reuse."""
        try:
            # Clean up any state from the crawler before returning it
            # The browser should be reused, but we might want to clear cookies/cache
            await self._available_crawlers.put(crawler)
        except Exception as e:
            logger.error(f"Error returning crawler to pool: {e}")
            # Don't put a potentially broken crawler back in the pool
            if crawler in self._crawlers:
                self._crawlers.remove(crawler)
                try:
                    await crawler.aclose()
                except Exception:
                    pass

    async def cleanup(self):
        """Close all browsers and cleanup the pool."""
        logger.info("Cleaning up browser pool...")

        # Close all crawlers
        for crawler in self._crawlers:
            try:
                await crawler.aclose()
            except Exception as e:
                logger.warning(f"Error closing crawler: {e}")

        # Clear the pool
        self._crawlers.clear()

        # Clear the queue
        while not self._available_crawlers.empty():
            try:
                self._available_crawlers.get_nowait()
            except asyncio.QueueEmpty:
                break

        self._initialized = False
        logger.info("Browser pool cleanup completed")

    async def monitor_browser_processes(self):
        """Monitor and log browser process information."""
        try:
            chrome_processes = []
            for proc in psutil.process_iter(["pid", "name", "memory_info"]):
                try:
                    if (
                        "chrome" in proc.info["name"].lower()
                        or "chromium" in proc.info["name"].lower()
                    ):
                        memory_mb = proc.info["memory_info"].rss / 1024 / 1024
                        chrome_processes.append(
                            {
                                "pid": proc.info["pid"],
                                "name": proc.info["name"],
                                "memory_mb": memory_mb,
                            }
                        )
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_processes:
                total_memory = sum(p["memory_mb"] for p in chrome_processes)
                logger.info(
                    f"Browser processes: {len(chrome_processes)}, Total memory: {total_memory:.2f}MB"
                )

                # Log warning if too many browser processes
                if len(chrome_processes) > self.pool_size * 2:
                    logger.warning(
                        f"High number of browser processes detected: {len(chrome_processes)}"
                    )

        except Exception as e:
            logger.error(f"Error monitoring browser processes: {e}")

    async def health_check(self):
        """Perform health check on browser pool."""
        try:
            active_crawlers = len(self._crawlers)
            available_crawlers = self._available_crawlers.qsize()

            logger.debug(
                f"Browser pool health: {active_crawlers} total, {available_crawlers} available"
            )

            # If we have fewer active crawlers than expected, log a warning
            if active_crawlers < self.pool_size:
                logger.warning(
                    f"Browser pool undersize: {active_crawlers}/{self.pool_size}"
                )

        except Exception as e:
            logger.error(f"Error during browser pool health check: {e}")
