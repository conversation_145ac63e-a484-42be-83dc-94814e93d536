"""
Dependency injection providers for the application.

This module provides dependency injection functions for FastAPI to replace
global singleton instances with proper DI pattern.
"""

from typing import Annotated

from fastapi import Depends

from app.container import get_service_container
from app.services.cache import CacheService


def get_cache_service() -> CacheService:
    """
    Get cache service instance from service container.
    """
    container = get_service_container()
    return container.cache_service


# Type aliases for dependency injection
CacheServiceDep = Annotated[CacheService, Depends(get_cache_service)]
