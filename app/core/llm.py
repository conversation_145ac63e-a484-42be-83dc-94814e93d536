import asyncio
import logging
from typing import Dict, <PERSON><PERSON>

from langchain_openai import Chat<PERSON>penA<PERSON>

logger = logging.getLogger(__name__)


class LLMService:
    """LLM service with proper instance-based dependency injection."""

    def __init__(self, max_size: int = 5):
        self._instances: Dict[Tuple[str, float], ChatOpenAI] = {}
        self.max_size = max_size

    def get_client(
        self, model: str = "gpt-4o-mini", temperature: float = 0.0
    ) -> ChatOpenAI:
        """
        Get or create a ChatOpenAI client instance.

        Args:
            model: The model name to use
            temperature: The temperature setting for the model

        Returns:
            ChatOpenAI instance
        """
        # Create a key for this configuration
        key = (model, temperature)

        # Return existing instance if available
        if key in self._instances:
            return self._instances[key]

        # Create new instance and cache it
        client = ChatOpenAI(model=model, temperature=temperature)
        self._instances[key] = client

        # Limit the cache size to prevent unbounded growth
        if len(self._instances) > self.max_size:
            # Remove the oldest instance (first item)
            self._instances.pop(next(iter(self._instances)))

        return client

    def clear_cache(self):
        """Clear all cached LLM client instances."""
        self._instances.clear()

    async def cleanup(self):
        """Cleanup LLM service resources."""
        # Properly dispose of ChatOpenAI clients
        for client in self._instances.values():
            # Close any internal HTTP clients if they exist
            if hasattr(client, "client") and hasattr(client.client, "close"):
                try:
                    if hasattr(client.client.close, "__call__"):
                        if asyncio.iscoroutinefunction(client.client.close):
                            await client.client.close()
                        else:
                            client.client.close()
                except Exception as e:
                    logger.warning(f"Error closing LLM client connection: {e}")

        self.clear_cache()
        logger.info("LLM service cleanup completed")


# Legacy function for backward compatibility - will be removed after refactoring
def get_llm_client(model: str = "gpt-4o-mini", temperature: float = 0.0) -> ChatOpenAI:
    """Legacy function - use dependency injection instead."""
    return ChatOpenAI(model=model, temperature=temperature)
