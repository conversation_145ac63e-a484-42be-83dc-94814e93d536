from __future__ import annotations

import asyncio
import logging
from typing import Any

from langchain_openai import ChatOpenAI

from app.config import AppSettings

logger = logging.getLogger(__name__)


class LLMService:
    """Refactored LLM service with configuration-based limits."""
    
    def __init__(self, settings: AppSettings):
        self.settings = settings
        self._instances: dict[tuple[str, float], ChatOpenAI] = {}
        self._max_instances = 10  # Configurable limit
    
    def get_client(self, model: str = "gpt-4o-mini", temperature: float = 0.0) -> ChatOpenAI:
        """Get or create ChatOpenAI client with instance limit management."""
        key = (model, temperature)
        
        if key in self._instances:
            return self._instances[key]
        
        # Check instance limit
        if len(self._instances) >= self._max_instances:
            # Remove oldest instance (LRU eviction)
            oldest_key = next(iter(self._instances))
            old_client = self._instances.pop(oldest_key)
            self._cleanup_client(old_client)
            logger.debug(f"Evicted LLM client for {oldest_key}")
        
        # Create new client with settings
        client = ChatOpenAI(
            model=model,
            temperature=temperature,
            api_key=self.settings.openai_api_key,
            timeout=self.settings.request.timeout,
        )
        
        self._instances[key] = client
        logger.debug(f"Created new LLM client for {key}")
        return client
    
    def clear_cache(self) -> None:
        """Clear all cached client instances."""
        for client in self._instances.values():
            self._cleanup_client(client)
        self._instances.clear()
        logger.info("LLM client cache cleared")
    
    def _cleanup_client(self, client: ChatOpenAI) -> None:
        """Clean up individual LLM client."""
        try:
            if hasattr(client, "client") and hasattr(client.client, "close"):
                if asyncio.iscoroutinefunction(client.client.close):
                    # Schedule cleanup for async close
                    asyncio.create_task(client.client.close())
                else:
                    client.client.close()
        except Exception as e:
            logger.warning(f"Error cleaning up LLM client: {e}")
    
    async def cleanup(self) -> None:
        """Async cleanup of all resources."""
        cleanup_tasks = []
        
        for client in self._instances.values():
            if hasattr(client, "client") and hasattr(client.client, "close"):
                if asyncio.iscoroutinefunction(client.client.close):
                    cleanup_tasks.append(client.client.close())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self._instances.clear()
        logger.info("LLM service cleanup completed")
