import asyncio
import logging

from cachetools import TTLCache

from app.container import get_service_container

logger = logging.getLogger(__name__)

# TTL cache for currency exchange rate
_currency_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL
_currency_lock = asyncio.Lock()


def round_to_million(value):
    """Round salary value to the nearest million"""
    if value == 0:
        return 0
    return round(value / 1000000) * 1000000


async def get_currency_exchange_rate(src: str, dst: str):
    """Get currency exchange rate with TTL caching"""
    cache_key = f"{src.upper()}_{dst.upper()}"

    async with _currency_lock:
        if cache_key in _currency_cache:
            return _currency_cache[cache_key]

    http_client = get_service_container().http_client_service
    response = await http_client.client.get(
        f"https://open.er-api.com/v6/latest/{src.upper()}", timeout=5
    )

    result = None
    if response.status_code == 200:
        if response.json().get("result") == "success":
            result = response.json().get("rates", {}).get(dst.upper())

    # Cache the result (including None results for currency exchange)
    async with _currency_lock:
        _currency_cache[cache_key] = result

    return result
