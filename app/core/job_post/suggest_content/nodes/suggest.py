from pydantic import BaseModel

from app.core.job_post.suggest_content.prompt import SUGGEST_PROMPT
from app.core.job_post.suggest_content.state import SuggestState
from app.core.job_post.suggest_content.utils import postprocess_content_suggest
from app.container import get_service_container
from app.core.utils import round_to_million
from app.helpers.get_suggest_salary import get_suggest_salary


class SuggestContent(BaseModel):
    job_title: str
    job_description: str
    job_mandatory_requirement: str
    job_should_have_requirement: str


async def suggest(state: SuggestState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=1.0)
    chain = llm.with_structured_output(SuggestContent)
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}"
    )
    messages = [
        {"role": "system", "content": SUGGEST_PROMPT},
        {"role": "user", "content": user_prompt},
    ]

    response: SuggestContent = await chain.ainvoke(messages)
    job_post_suggest = response.model_dump()
    new_job_post_suggest = postprocess_content_suggest(job_post_suggest, state.__dict__)

    data = {"data": [{"jobId": "", "jobTitle": state.job_title}]}
    salary_response = await get_suggest_salary(data)
    if salary_response:
        salary_min = salary_response["data"][0]["minSalary"]
        salary_max = salary_response["data"][0]["maxSalary"]
        salary_min = round_to_million(salary_min)
        salary_max = round_to_million(salary_max)
        salary = {
            "salary_min": float(salary_min),
            "salary_max": float(salary_max),
            "salary_currency": "Vnd",
        }
    else:
        salary = {}
    new_job_post_suggest.update({"salary": salary})
    return {"job_post_suggest": new_job_post_suggest}
