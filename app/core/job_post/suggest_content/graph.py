from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_content.nodes import suggest
from app.core.job_post.suggest_content.state import (
    SuggestInput,
    SuggestOutput,
    SuggestState,
)


# Global cached graph instance
_compiled_graph = None


def get_suggest_content_graph():
    """Get cached suggest content graph to prevent creating new graphs for each request."""
    global _compiled_graph

    if _compiled_graph is None:
        workflow = StateGraph(SuggestState, input=SuggestInput, output=SuggestOutput)

        workflow.add_node("suggest", suggest)

        workflow.add_edge(START, "suggest")
        workflow.add_edge("suggest", END)

        _compiled_graph = workflow.compile()
        _compiled_graph.name = "Suggest Content"

    return _compiled_graph
