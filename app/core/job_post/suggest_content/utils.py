# Tokenize function
import difflib
import re
import uuid


def tokenize(text):
    return re.findall(r"\S+|\n", text)


def get_diff(original, revised_sentence):
    transform_revised_sentence = revised_sentence.replace(". -", ".\n-").replace(
        ". •", ".\n•"
    )
    original_tokens = tokenize(original)
    revised_tokens = tokenize(transform_revised_sentence)

    # Use SequenceMatcher
    sm = difflib.SequenceMatcher(None, original_tokens, revised_tokens)

    # Process opcodes
    revised_words = []
    for tag, i1, i2, j1, j2 in sm.get_opcodes():
        if tag == "replace":
            original_segment = original_tokens[i1:i2]
            revised_segment = revised_tokens[j1:j2]
            original_word = " ".join(original_segment)

            revised_word = " ".join(revised_segment)
            if i2 - i1 == 1:
                index = str(i1)
                # Assign IDs to match example
                entry_id = str(uuid.uuid4())
            else:
                index = f"{i1}-{i2 - 1}"
                entry_id = str(uuid.uuid4())
            if original_word.endswith("\n"):
                if not revised_word.endswith("\n"):
                    revised_word += "\n"
            entry = {
                "id": entry_id,
                "index": index,
                "revised": revised_word,
                "word": original_word,
            }
            revised_words.append(entry)

    # Format output
    output = {
        "original": original,
        "revised_words": revised_words,
        "revised": revised_sentence,
    }
    return output


def postprocess_content_suggest(job_post_suggest: dict, original: dict):
    new_job_post_suggest = {}
    job_title_before = original.get("job_title")
    job_title_after = job_post_suggest.get("job_title")
    if not job_title_before == job_title_after:
        diff = get_diff(job_title_before, job_title_after)
        new_job_post_suggest.update({"job_title": diff})
    else:
        new_job_post_suggest.update({"job_title": None})

    job_description_before = original.get("job_description")
    job_description_after = job_post_suggest.get("job_description")
    if not job_description_before == job_description_after:
        diff = get_diff(job_description_before, job_description_after)
        new_job_post_suggest.update({"job_description": diff})
    else:
        new_job_post_suggest.update({"job_description": None})
    job_mandatory_requirement_before = original.get("job_mandatory_requirement")
    job_mandatory_requirement_after = job_post_suggest.get("job_mandatory_requirement")
    if not job_mandatory_requirement_before == job_mandatory_requirement_after:
        diff = get_diff(
            job_mandatory_requirement_before, job_mandatory_requirement_after
        )
        new_job_post_suggest.update({"job_mandatory_requirement": diff})
    else:
        new_job_post_suggest.update({"job_mandatory_requirement": None})

    job_should_have_requirement_before = original.get("job_should_have_requirement")
    job_should_have_requirement_after = job_post_suggest.get(
        "job_should_have_requirement"
    )
    if not job_should_have_requirement_before == job_should_have_requirement_after:
        diff = get_diff(
            job_should_have_requirement_before, job_should_have_requirement_after
        )
        new_job_post_suggest.update({"job_should_have_requirement": diff})
    else:
        new_job_post_suggest.update({"job_should_have_requirement": None})

    return new_job_post_suggest
