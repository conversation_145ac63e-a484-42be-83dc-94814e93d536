"""
Dependency injection providers for job post workflows.

Provides factory functions and dependency injection for the new
workflow architecture, replacing the global service container pattern.
"""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends

from app.config import AppSettings, get_settings
from app.container import get_service_container
from app.core.job_post.factory import (
    GraphFactory,
    NodeFactory,
    WorkflowSettings,
)
from app.core.job_post.factory.workflow_config import get_workflow_settings_for_type
from app.core.job_post.services import (
    UnifiedCacheService,
    ModelCacheService,
    EnumCacheService,
)
from app.core.job_post.services.unified_cache import JobPostCacheService


def get_workflow_settings(settings: Annotated[AppSettings, Depends(get_settings)]) -> WorkflowSettings:
    """Get workflow settings from app configuration."""
    return settings.workflow


def get_unified_cache_service(
    settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> UnifiedCacheService:
    """Get unified cache service for job post workflows."""
    # Get the main cache service from container
    container = get_service_container()
    
    return JobPostCacheService(
        cache_service=container.cache_service,
        settings=settings,
        cache_prefix="job_post"
    )


def get_model_cache_service(
    unified_cache: Annotated[UnifiedCacheService, Depends(get_unified_cache_service)]
) -> ModelCacheService:
    """Get model cache service."""
    return ModelCacheService(unified_cache)


def get_enum_cache_service(
    unified_cache: Annotated[UnifiedCacheService, Depends(get_unified_cache_service)]
) -> EnumCacheService:
    """Get enum cache service."""
    return EnumCacheService(unified_cache)


@lru_cache(maxsize=1)
def get_node_factory(
    settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> NodeFactory:
    """Get node factory with injected services."""
    container = get_service_container()
    
    return NodeFactory(
        llm_service=container.llm_service,
        cache_service=container.cache_service,
        http_client_service=container.http_client_service,
        browser_pool_service=container.browser_pool_service,
        settings=settings
    )


@lru_cache(maxsize=1)
def get_graph_factory(
    node_factory: Annotated[NodeFactory, Depends(get_node_factory)],
    settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> GraphFactory:
    """Get graph factory with dependency injection."""
    return GraphFactory(
        node_factory=node_factory,
        settings=settings
    )


def get_parse_workflow_settings(
    base_settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> WorkflowSettings:
    """Get workflow settings optimized for parsing workflows."""
    return get_workflow_settings_for_type(base_settings, "parse")


def get_generate_workflow_settings(
    base_settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> WorkflowSettings:
    """Get workflow settings optimized for generation workflows."""
    return get_workflow_settings_for_type(base_settings, "generate")


def get_suggest_workflow_settings(
    base_settings: Annotated[WorkflowSettings, Depends(get_workflow_settings)]
) -> WorkflowSettings:
    """Get workflow settings optimized for suggestion workflows."""
    return get_workflow_settings_for_type(base_settings, "suggest")


# Type aliases for dependency injection
WorkflowSettingsDep = Annotated[WorkflowSettings, Depends(get_workflow_settings)]
UnifiedCacheDep = Annotated[UnifiedCacheService, Depends(get_unified_cache_service)]
ModelCacheDep = Annotated[ModelCacheService, Depends(get_model_cache_service)]
EnumCacheDep = Annotated[EnumCacheService, Depends(get_enum_cache_service)]
NodeFactoryDep = Annotated[NodeFactory, Depends(get_node_factory)]
GraphFactoryDep = Annotated[GraphFactory, Depends(get_graph_factory)]

ParseWorkflowSettingsDep = Annotated[WorkflowSettings, Depends(get_parse_workflow_settings)]
GenerateWorkflowSettingsDep = Annotated[WorkflowSettings, Depends(get_generate_workflow_settings)]
SuggestWorkflowSettingsDep = Annotated[WorkflowSettings, Depends(get_suggest_workflow_settings)]
