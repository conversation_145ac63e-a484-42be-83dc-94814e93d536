"""
Node factory for creating workflow nodes with dependency injection.

Provides a centralized way to create node functions with injected services,
eliminating the need for nodes to call get_service_container() directly.
"""

import asyncio
import logging
from typing import Callable, Any, Dict, Optional
from functools import wraps

from app.core.llm import LLMService
from app.services.cache import CacheService
from app.services.http_client import HTTPClientService
from app.services.browser_pool import BrowserPoolService
from .workflow_config import WorkflowSettings

logger = logging.getLogger(__name__)


class NodeFactory:
    """Factory for creating workflow nodes with dependency injection."""
    
    def __init__(
        self,
        llm_service: LLMService,
        cache_service: CacheService,
        http_client_service: HTTPClientService,
        browser_pool_service: BrowserPoolService,
        settings: WorkflowSettings
    ):
        self.llm_service = llm_service
        self.cache_service = cache_service
        self.http_client_service = http_client_service
        self.browser_pool_service = browser_pool_service
        self.settings = settings
    
    def create_node(
        self, 
        node_func: Callable,
        timeout: Optional[int] = None,
        retry_attempts: Optional[int] = None
    ) -> Callable:
        """
        Create a node function with injected dependencies and error handling.
        
        Args:
            node_func: The original node function
            timeout: Custom timeout for this node (uses settings default if None)
            retry_attempts: Custom retry attempts (uses settings default if None)
            
        Returns:
            Wrapped node function with dependency injection
        """
        node_timeout = timeout or self.settings.node_timeout
        max_retries = retry_attempts or self.settings.max_retry_attempts
        
        @wraps(node_func)
        async def wrapped_node(state: Any) -> Any:
            """Wrapped node function with dependency injection and error handling."""
            
            # Create services context for the node
            services = NodeServices(
                llm=self.llm_service,
                cache=self.cache_service,
                http_client=self.http_client_service,
                browser_pool=self.browser_pool_service,
                settings=self.settings
            )
            
            # Execute with timeout and retry logic
            for attempt in range(max_retries):
                try:
                    # Execute node with timeout
                    result = await asyncio.wait_for(
                        node_func(state, services),
                        timeout=node_timeout
                    )
                    return result
                    
                except asyncio.TimeoutError:
                    logger.warning(
                        f"Node {node_func.__name__} timed out after {node_timeout}s "
                        f"(attempt {attempt + 1}/{max_retries})"
                    )
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(self.settings.retry_wait_seconds)
                    
                except Exception as e:
                    logger.error(
                        f"Node {node_func.__name__} failed on attempt {attempt + 1}/{max_retries}: {e}"
                    )
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(self.settings.retry_wait_seconds)
            
            # This should never be reached due to the raise in the except blocks
            raise RuntimeError(f"Node {node_func.__name__} failed after {max_retries} attempts")
        
        return wrapped_node
    
    def create_simple_node(self, node_func: Callable) -> Callable:
        """
        Create a simple node without timeout/retry logic for lightweight operations.
        
        Args:
            node_func: The original node function
            
        Returns:
            Wrapped node function with dependency injection only
        """
        @wraps(node_func)
        async def wrapped_node(state: Any) -> Any:
            """Simple wrapped node function with dependency injection only."""
            services = NodeServices(
                llm=self.llm_service,
                cache=self.cache_service,
                http_client=self.http_client_service,
                browser_pool=self.browser_pool_service,
                settings=self.settings
            )
            return await node_func(state, services)
        
        return wrapped_node


class NodeServices:
    """Container for services available to node functions."""
    
    def __init__(
        self,
        llm: LLMService,
        cache: CacheService,
        http_client: HTTPClientService,
        browser_pool: BrowserPoolService,
        settings: WorkflowSettings
    ):
        self.llm = llm
        self.cache = cache
        self.http_client = http_client
        self.browser_pool = browser_pool
        self.settings = settings
    
    def get_llm_client(self, model: str = "gpt-4o-mini", temperature: float = 0.0):
        """Get LLM client with specified parameters."""
        return self.llm.get_client(model=model, temperature=temperature)
    
    async def cache_get(self, key: str) -> Any:
        """Get value from cache."""
        return await self.cache.get(key)
    
    async def cache_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        return await self.cache.set(key, value, ttl or self.settings.cache_ttl)
    
    async def http_get(self, url: str, **kwargs) -> Any:
        """Make HTTP GET request."""
        return await self.http_client.client.get(url, **kwargs)
    
    async def http_post(self, url: str, **kwargs) -> Any:
        """Make HTTP POST request."""
        return await self.http_client.client.post(url, **kwargs)


def create_node_factory(
    llm_service: LLMService,
    cache_service: CacheService,
    http_client_service: HTTPClientService,
    browser_pool_service: BrowserPoolService,
    settings: WorkflowSettings
) -> NodeFactory:
    """
    Factory function to create a NodeFactory instance.
    
    This function can be used with dependency injection frameworks.
    """
    return NodeFactory(
        llm_service=llm_service,
        cache_service=cache_service,
        http_client_service=http_client_service,
        browser_pool_service=browser_pool_service,
        settings=settings
    )
