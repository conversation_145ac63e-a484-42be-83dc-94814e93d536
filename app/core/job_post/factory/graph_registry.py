"""
Graph registry for managing compiled graph instances.

Provides centralized management of graph instances with configurable limits,
TTL-based expiration, and automatic cleanup to prevent memory leaks.
"""

import asyncio
import logging
import time
import weakref
from typing import Dict, Optional, Any, Callable
from dataclasses import dataclass

from langgraph.graph import CompiledGraph

from .workflow_config import WorkflowSettings

logger = logging.getLogger(__name__)


@dataclass
class GraphEntry:
    """Entry for a cached graph instance."""
    graph: CompiledGraph
    created_at: float
    last_accessed: float
    access_count: int = 0
    
    def is_expired(self, ttl: int) -> bool:
        """Check if the graph entry has expired."""
        return time.time() - self.created_at > ttl
    
    def touch(self) -> None:
        """Update last accessed time and increment access count."""
        self.last_accessed = time.time()
        self.access_count += 1


class GraphRegistry:
    """Registry for managing compiled graph instances with limits and TTL."""
    
    def __init__(self, settings: WorkflowSettings):
        self.settings = settings
        self._graphs: Dict[str, GraphEntry] = {}
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._finalizer = weakref.finalize(self, self._cleanup_sync)
        
    async def get_graph(
        self, 
        key: str, 
        factory_func: Callable[[], CompiledGraph]
    ) -> CompiledGraph:
        """
        Get a graph instance, creating it if necessary.
        
        Args:
            key: Unique identifier for the graph
            factory_func: Function to create the graph if not cached
            
        Returns:
            Compiled graph instance
        """
        async with self._lock:
            # Check if graph exists and is not expired
            if key in self._graphs:
                entry = self._graphs[key]
                if not entry.is_expired(self.settings.graph_cache_ttl):
                    entry.touch()
                    logger.debug(f"Retrieved cached graph '{key}' (access count: {entry.access_count})")
                    return entry.graph
                else:
                    # Remove expired graph
                    del self._graphs[key]
                    logger.debug(f"Removed expired graph '{key}'")
            
            # Check if we need to make room for new graph
            if len(self._graphs) >= self.settings.max_graph_instances:
                await self._evict_oldest()
            
            # Create new graph
            logger.debug(f"Creating new graph '{key}'")
            graph = factory_func()
            
            # Store in registry
            entry = GraphEntry(
                graph=graph,
                created_at=time.time(),
                last_accessed=time.time()
            )
            entry.touch()
            self._graphs[key] = entry
            
            # Start cleanup task if not running
            if self._cleanup_task is None or self._cleanup_task.done():
                self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            logger.info(f"Created and cached graph '{key}' (total graphs: {len(self._graphs)})")
            return graph
    
    async def remove_graph(self, key: str) -> bool:
        """Remove a specific graph from the registry."""
        async with self._lock:
            if key in self._graphs:
                del self._graphs[key]
                logger.debug(f"Manually removed graph '{key}'")
                return True
            return False
    
    async def clear_all(self) -> None:
        """Clear all graphs from the registry."""
        async with self._lock:
            count = len(self._graphs)
            self._graphs.clear()
            logger.info(f"Cleared all graphs from registry ({count} graphs removed)")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        async with self._lock:
            current_time = time.time()
            stats = {
                "total_graphs": len(self._graphs),
                "max_instances": self.settings.max_graph_instances,
                "cache_ttl": self.settings.graph_cache_ttl,
                "graphs": {}
            }
            
            for key, entry in self._graphs.items():
                age = current_time - entry.created_at
                last_access_age = current_time - entry.last_accessed
                stats["graphs"][key] = {
                    "age_seconds": age,
                    "last_access_seconds_ago": last_access_age,
                    "access_count": entry.access_count,
                    "expired": entry.is_expired(self.settings.graph_cache_ttl)
                }
            
            return stats
    
    async def _evict_oldest(self) -> None:
        """Evict the oldest graph to make room for a new one."""
        if not self._graphs:
            return
        
        # Find the oldest graph by creation time
        oldest_key = min(self._graphs.keys(), key=lambda k: self._graphs[k].created_at)
        del self._graphs[oldest_key]
        logger.debug(f"Evicted oldest graph '{oldest_key}' to make room")
    
    async def _periodic_cleanup(self) -> None:
        """Periodically clean up expired graphs."""
        try:
            while True:
                await asyncio.sleep(self.settings.graph_cache_ttl // 4)  # Check every quarter TTL
                await self._cleanup_expired()
        except asyncio.CancelledError:
            logger.debug("Graph registry cleanup task cancelled")
        except Exception as e:
            logger.error(f"Error in graph registry cleanup: {e}")
    
    async def _cleanup_expired(self) -> None:
        """Remove expired graphs from the registry."""
        async with self._lock:
            expired_keys = [
                key for key, entry in self._graphs.items()
                if entry.is_expired(self.settings.graph_cache_ttl)
            ]
            
            for key in expired_keys:
                del self._graphs[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired graphs: {expired_keys}")
    
    def _cleanup_sync(self) -> None:
        """Synchronous cleanup for finalizer."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
    
    async def shutdown(self) -> None:
        """Shutdown the registry and cleanup resources."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.clear_all()
        logger.info("Graph registry shutdown complete")
