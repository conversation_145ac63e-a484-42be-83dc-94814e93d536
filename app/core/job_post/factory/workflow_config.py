"""
Workflow configuration settings for job post processing.

Provides configuration-based limits and settings for workflow execution,
graph management, and resource constraints.
"""

from typing import Any, Dict

from app.config.settings import WorkflowSettings

# Re-export the main WorkflowSettings for compatibility
__all__ = ["WorkflowSettings", "get_workflow_settings_for_type"]


def get_workflow_settings_for_type(
    base_settings: WorkflowSettings, workflow_type: str
) -> WorkflowSettings:
    """
    Get workflow-specific settings based on workflow type.

    Args:
        base_settings: Base workflow settings from app configuration
        workflow_type: Type of workflow (parse, generate, suggest)

    Returns:
        Customized workflow settings for the specific workflow type
    """
    # Create a dictionary of current settings
    settings_dict = {
        field.name: getattr(base_settings, field.name)
        for field in base_settings.__dataclass_fields__.values()
    }

    # Apply workflow-specific overrides
    if workflow_type in ["parse", "parse_v1", "parse_v2"]:
        settings_dict.update({
            "max_content_length": 100000,  # Larger content for parsing
            "node_timeout": 45,  # Longer timeout for complex parsing
        })
    elif workflow_type in ["generate", "generate_v2"]:
        settings_dict.update({
            "max_content_length": 20000,  # Smaller content for generation
            "node_timeout": 60,  # Longer timeout for generation
        })
    elif workflow_type in ["suggest_content", "suggest_skills", "suggest_job_function"]:
        settings_dict.update({
            "max_content_length": 10000,  # Small content for suggestions
            "node_timeout": 15,  # Quick suggestions
            "max_concurrent_workflows": 20,  # More concurrent suggestions
        })

    # Create new settings instance with overrides
    return WorkflowSettings(**settings_dict)
