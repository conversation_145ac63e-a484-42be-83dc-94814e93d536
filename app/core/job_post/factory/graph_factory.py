"""
Graph factory for creating and managing workflow graphs with dependency injection.

Provides centralized graph creation with proper dependency injection,
resource management, and configuration-based limits.
"""

import logging
from enum import Enum
from typing import Any, Callable, Dict, Optional

from langgraph.graph import END, START, CompiledGraph, StateGraph
from langgraph.pregel.main import RetryPolicy

from .graph_registry import GraphRegistry
from .node_factory import NodeFactory
from .workflow_config import WorkflowSettings

logger = logging.getLogger(__name__)


class WorkflowType(Enum):
    """Enumeration of available workflow types."""

    PARSE_V1 = "parse_v1"
    PARSE_V2 = "parse_v2"
    GENERATE_V2 = "generate_v2"
    SUGGEST_CONTENT = "suggest_content"
    SUGGEST_SKILLS = "suggest_skills"
    SUGGEST_JOB_FUNCTION = "suggest_job_function"


class GraphFactory:
    """Factory for creating and managing workflow graphs."""

    def __init__(self, node_factory: NodeFactory, settings: WorkflowSettings):
        self.node_factory = node_factory
        self.settings = settings
        self.registry = GraphRegistry(settings)
        self._graph_builders: Dict[WorkflowType, Callable] = {}
        self._register_graph_builders()

    def _register_graph_builders(self) -> None:
        """Register graph builder functions for each workflow type."""
        self._graph_builders = {
            WorkflowType.PARSE_V1: self._build_parse_v1_graph,
            WorkflowType.PARSE_V2: self._build_parse_v2_graph,
            WorkflowType.GENERATE_V2: self._build_generate_v2_graph,
            WorkflowType.SUGGEST_CONTENT: self._build_suggest_content_graph,
            WorkflowType.SUGGEST_SKILLS: self._build_suggest_skills_graph,
            WorkflowType.SUGGEST_JOB_FUNCTION: self._build_suggest_job_function_graph,
        }

    async def get_graph(
        self,
        workflow_type: WorkflowType,
        state_class: Any,
        input_class: Optional[Any] = None,
        output_class: Optional[Any] = None,
        **kwargs,
    ) -> CompiledGraph:
        """
        Get a compiled graph for the specified workflow type.

        Args:
            workflow_type: Type of workflow to create
            state_class: State class for the workflow
            input_class: Input class for the workflow (optional)
            output_class: Output class for the workflow (optional)
            **kwargs: Additional arguments for graph creation

        Returns:
            Compiled graph instance
        """
        # Create cache key based on workflow type and parameters
        cache_key = f"{workflow_type.value}_{
            hash((
                state_class.__name__,
                input_class.__name__ if input_class else None,
                output_class.__name__ if output_class else None,
            ))
        }"

        # Get graph from registry (creates if not exists)
        return await self.registry.get_graph(
            cache_key,
            lambda: self._create_graph(
                workflow_type, state_class, input_class, output_class, **kwargs
            ),
        )

    def _create_graph(
        self,
        workflow_type: WorkflowType,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Create a new graph instance."""
        if workflow_type not in self._graph_builders:
            raise ValueError(f"Unknown workflow type: {workflow_type}")

        # Create the graph using the appropriate builder
        builder = self._graph_builders[workflow_type]
        graph = builder(state_class, input_class, output_class, **kwargs)

        # Set graph name
        graph.name = f"Job Post {workflow_type.value.replace('_', ' ').title()}"

        logger.info(f"Created new graph for workflow type: {workflow_type.value}")
        return graph

    def _build_parse_v1_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build parse v1 workflow graph."""
        # Import node functions (will be updated to use DI)
        from app.core.job_post.parse.nodes import (
            parse_job_extra_info,
            parse_job_info,
            postprocess,
            suggest_content,
            suggest_job_function,
            suggest_skills,
            validate_content,
        )

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Create retry policy
        retry = RetryPolicy(max_attempts=self.settings.max_retry_attempts)

        # Add nodes with dependency injection
        workflow.add_node(
            "validate_content", self.node_factory.create_simple_node(validate_content)
        )
        workflow.add_node(
            "parse_job_info", self.node_factory.create_node(parse_job_info), retry=retry
        )
        workflow.add_node(
            "parse_job_extra_info",
            self.node_factory.create_node(parse_job_extra_info),
            retry=retry,
        )
        workflow.add_node(
            "suggest_content", self.node_factory.create_node(suggest_content)
        )
        workflow.add_node(
            "suggest_skills", self.node_factory.create_node(suggest_skills)
        )
        workflow.add_node(
            "suggest_job_function", self.node_factory.create_node(suggest_job_function)
        )
        workflow.add_node(
            "postprocess", self.node_factory.create_simple_node(postprocess)
        )

        # Add edges
        workflow.add_edge(START, "validate_content")
        workflow.add_conditional_edges("validate_content", self._should_continue_parse)
        workflow.add_edge("parse_job_info", "suggest_content")
        workflow.add_edge("parse_job_info", "suggest_skills")
        workflow.add_edge("parse_job_info", "suggest_job_function")
        workflow.add_edge(
            [
                "parse_job_extra_info",
                "suggest_content",
                "suggest_skills",
                "suggest_job_function",
            ],
            "postprocess",
        )
        workflow.add_edge("postprocess", END)

        return workflow.compile()

    def _build_parse_v2_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build parse v2 workflow graph."""
        # Import node functions (will be updated to use DI)
        from app.core.job_post.parse.nodes import (
            parse_job_extra_info,
            parse_job_info,
            postprocess,
            suggest_job_function,
            suggest_skills,
            validate_content,
        )

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Create retry policy
        retry = RetryPolicy(max_attempts=self.settings.max_retry_attempts)

        # Add nodes with dependency injection
        workflow.add_node(
            "validate_content", self.node_factory.create_simple_node(validate_content)
        )
        workflow.add_node(
            "parse_job_info", self.node_factory.create_node(parse_job_info), retry=retry
        )
        workflow.add_node(
            "parse_job_extra_info",
            self.node_factory.create_node(parse_job_extra_info),
            retry=retry,
        )
        workflow.add_node(
            "suggest_skills", self.node_factory.create_node(suggest_skills)
        )
        workflow.add_node(
            "suggest_job_function", self.node_factory.create_node(suggest_job_function)
        )
        workflow.add_node(
            "postprocess", self.node_factory.create_simple_node(postprocess)
        )

        # Add edges
        workflow.add_edge(START, "validate_content")
        workflow.add_conditional_edges("validate_content", self._should_continue_parse)
        workflow.add_edge("parse_job_info", "suggest_skills")
        workflow.add_edge("parse_job_info", "suggest_job_function")
        workflow.add_edge(
            ["parse_job_extra_info", "suggest_skills", "suggest_job_function"],
            "postprocess",
        )
        workflow.add_edge("postprocess", END)

        return workflow.compile()

    def _should_continue_parse(self, state: Any) -> Any:
        """Conditional logic for parse workflows."""
        if not state.is_valid_content:
            return END
        return ["parse_job_info", "parse_job_extra_info"]

    def _build_generate_v2_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build generate v2 workflow graph."""
        from app.core.job_post.generate_v2.nodes import (
            generate_job_post,
            postprocess,
            suggest_job_function,
            suggest_job_title,
            suggest_skills,
            validate_job_title,
        )

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Add nodes with dependency injection
        workflow.add_node(
            "validate_job_title",
            self.node_factory.create_simple_node(validate_job_title),
        )
        workflow.add_node(
            "suggest_job_title", self.node_factory.create_node(suggest_job_title)
        )
        workflow.add_node(
            "generate_job_post", self.node_factory.create_node(generate_job_post)
        )
        workflow.add_node(
            "suggest_skills", self.node_factory.create_node(suggest_skills)
        )
        workflow.add_node(
            "suggest_job_function", self.node_factory.create_node(suggest_job_function)
        )
        workflow.add_node(
            "postprocess", self.node_factory.create_simple_node(postprocess)
        )

        # Add edges
        workflow.add_edge(START, "validate_job_title")
        workflow.add_conditional_edges(
            "validate_job_title",
            self._should_continue_generate,
            ["suggest_job_title", END],
        )
        workflow.add_edge("suggest_job_title", "generate_job_post")
        workflow.add_edge("generate_job_post", "suggest_skills")
        workflow.add_edge("generate_job_post", "suggest_job_function")
        workflow.add_edge(["suggest_skills", "suggest_job_function"], "postprocess")
        workflow.add_edge("postprocess", END)

        return workflow.compile()

    def _should_continue_generate(self, state: Any) -> Any:
        """Conditional logic for generate workflows."""
        if not state.is_valid_job_title:
            return END
        return "suggest_job_title"

    def _build_suggest_content_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build suggest content workflow graph."""
        from app.core.job_post.suggest_content.nodes import suggest

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Add nodes
        workflow.add_node("suggest", self.node_factory.create_node(suggest))

        # Add edges
        workflow.add_edge(START, "suggest")
        workflow.add_edge("suggest", END)

        return workflow.compile()

    def _build_suggest_skills_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build suggest skills workflow graph."""
        from app.core.job_post.suggest_skills.nodes import suggest_skills

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Add nodes
        workflow.add_node(
            "suggest_skills", self.node_factory.create_node(suggest_skills)
        )

        # Add edges
        workflow.add_edge(START, "suggest_skills")
        workflow.add_edge("suggest_skills", END)

        return workflow.compile()

    def _build_suggest_job_function_graph(
        self,
        state_class: Any,
        input_class: Optional[Any],
        output_class: Optional[Any],
        **kwargs,
    ) -> CompiledGraph:
        """Build suggest job function workflow graph."""
        from app.core.job_post.suggest_job_function.nodes import suggest_job_function

        # Create workflow
        workflow_args = {"state": state_class}
        if input_class:
            workflow_args["input"] = input_class
        if output_class:
            workflow_args["output"] = output_class

        workflow = StateGraph(**workflow_args)

        # Add nodes
        workflow.add_node(
            "suggest_job_function", self.node_factory.create_node(suggest_job_function)
        )

        # Add edges
        workflow.add_edge(START, "suggest_job_function")
        workflow.add_edge("suggest_job_function", END)

        return workflow.compile()

    async def get_registry_stats(self) -> Dict[str, Any]:
        """Get statistics about the graph registry."""
        return await self.registry.get_stats()

    async def clear_cache(self) -> None:
        """Clear all cached graphs."""
        await self.registry.clear_all()

    async def shutdown(self) -> None:
        """Shutdown the graph factory and cleanup resources."""
        await self.registry.shutdown()
        logger.info("Graph factory shutdown complete")
