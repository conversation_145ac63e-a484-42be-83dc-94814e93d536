"""
Workflow context manager for managing entire workflow execution lifecycle.

Provides automatic resource management, error handling, and cleanup
for complete workflow execution sessions.
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional, AsyncGenerator
from contextlib import asynccontextmanager

from app.core.job_post.factory.workflow_config import WorkflowSettings
from app.core.job_post.factory.graph_factory import GraphFactory, WorkflowType
from .cache_context import CacheContext
from .file_context import FileContext

logger = logging.getLogger(__name__)


class WorkflowContext:
    """Context manager for workflow execution with automatic resource management."""
    
    def __init__(
        self,
        workflow_type: WorkflowType,
        graph_factory: GraphFactory,
        settings: WorkflowSettings,
        workflow_id: Optional[str] = None
    ):
        self.workflow_type = workflow_type
        self.graph_factory = graph_factory
        self.settings = settings
        self.workflow_id = workflow_id or f"{workflow_type.value}_{int(time.time())}"
        
        # Context managers
        self.cache_context: Optional[CacheContext] = None
        self.file_context: Optional[FileContext] = None
        
        # Execution tracking
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.error: Optional[Exception] = None
        self.result: Any = None
        
    async def __aenter__(self) -> "WorkflowContext":
        """Enter the workflow context."""
        self.start_time = time.time()
        logger.info(f"Starting workflow {self.workflow_id} (type: {self.workflow_type.value})")
        
        try:
            # Initialize context managers
            self.cache_context = CacheContext(
                cache_prefix=f"workflow_{self.workflow_id}",
                default_ttl=self.settings.cache_ttl
            )
            await self.cache_context.__aenter__()
            
            self.file_context = FileContext(
                base_path=f"/tmp/workflow_{self.workflow_id}",
                auto_cleanup=True
            )
            await self.file_context.__aenter__()
            
            return self
            
        except Exception as e:
            logger.error(f"Failed to initialize workflow context {self.workflow_id}: {e}")
            await self._cleanup()
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the workflow context with cleanup."""
        self.end_time = time.time()
        duration = self.end_time - (self.start_time or self.end_time)
        
        if exc_type:
            self.error = exc_val
            logger.error(
                f"Workflow {self.workflow_id} failed after {duration:.2f}s: {exc_val}"
            )
        else:
            logger.info(
                f"Workflow {self.workflow_id} completed successfully in {duration:.2f}s"
            )
        
        await self._cleanup()
        return False  # Don't suppress exceptions
    
    async def _cleanup(self):
        """Cleanup all resources."""
        cleanup_tasks = []
        
        if self.file_context:
            cleanup_tasks.append(("File context", self.file_context.__aexit__(None, None, None)))
        
        if self.cache_context:
            cleanup_tasks.append(("Cache context", self.cache_context.__aexit__(None, None, None)))
        
        # Execute cleanup tasks
        for name, task in cleanup_tasks:
            try:
                await task
                logger.debug(f"{name} cleanup completed for workflow {self.workflow_id}")
            except Exception as e:
                logger.error(f"Error during {name} cleanup for workflow {self.workflow_id}: {e}")
    
    async def execute_workflow(
        self,
        graph_input: Dict[str, Any],
        state_class: Any,
        input_class: Optional[Any] = None,
        output_class: Optional[Any] = None
    ) -> Any:
        """
        Execute the workflow with the given input.
        
        Args:
            graph_input: Input data for the workflow
            state_class: State class for the workflow
            input_class: Input class for the workflow
            output_class: Output class for the workflow
            
        Returns:
            Workflow execution result
        """
        try:
            # Get the compiled graph
            graph = await self.graph_factory.get_graph(
                self.workflow_type,
                state_class,
                input_class,
                output_class
            )
            
            # Execute the workflow
            logger.debug(f"Executing workflow {self.workflow_id} with input keys: {list(graph_input.keys())}")
            self.result = await graph.ainvoke(graph_input)
            
            return self.result
            
        except Exception as e:
            logger.error(f"Workflow execution failed for {self.workflow_id}: {e}")
            raise
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics for the workflow."""
        duration = None
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
        
        return {
            "workflow_id": self.workflow_id,
            "workflow_type": self.workflow_type.value,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration_seconds": duration,
            "success": self.error is None,
            "error": str(self.error) if self.error else None,
            "has_result": self.result is not None
        }


@asynccontextmanager
async def workflow_execution(
    workflow_type: WorkflowType,
    graph_factory: GraphFactory,
    settings: WorkflowSettings,
    workflow_id: Optional[str] = None
) -> AsyncGenerator[WorkflowContext, None]:
    """
    Async context manager for workflow execution.
    
    Usage:
        async with workflow_execution(WorkflowType.PARSE_V2, factory, settings) as ctx:
            result = await ctx.execute_workflow(input_data, StateClass)
    """
    context = WorkflowContext(workflow_type, graph_factory, settings, workflow_id)
    async with context:
        yield context


@asynccontextmanager
async def batch_workflow_execution(
    workflow_configs: list[tuple[WorkflowType, Dict[str, Any]]],
    graph_factory: GraphFactory,
    settings: WorkflowSettings,
    max_concurrent: Optional[int] = None
) -> AsyncGenerator[list[Any], None]:
    """
    Execute multiple workflows concurrently with resource management.
    
    Args:
        workflow_configs: List of (workflow_type, input_data) tuples
        graph_factory: Graph factory instance
        settings: Workflow settings
        max_concurrent: Maximum concurrent workflows (uses settings default if None)
        
    Yields:
        List of workflow results in the same order as input
    """
    max_concurrent = max_concurrent or settings.max_concurrent_workflows
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def execute_single(workflow_type: WorkflowType, input_data: Dict[str, Any]) -> Any:
        async with semaphore:
            async with workflow_execution(workflow_type, graph_factory, settings) as ctx:
                # Note: This is a simplified version - in practice, you'd need to pass
                # the appropriate state/input/output classes based on workflow_type
                return await ctx.execute_workflow(input_data, None)  # TODO: Fix state class
    
    # Create tasks for all workflows
    tasks = [
        execute_single(workflow_type, input_data)
        for workflow_type, input_data in workflow_configs
    ]
    
    # Execute all workflows concurrently
    try:
        results = await asyncio.gather(*tasks)
        yield results
    except Exception as e:
        logger.error(f"Batch workflow execution failed: {e}")
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        raise
