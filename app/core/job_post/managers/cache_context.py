"""
Cache context manager for managing cache operations with automatic cleanup.

Provides scoped cache management with automatic key prefixing,
TTL management, and cleanup for workflow and node operations.
"""

import logging
from typing import Any, Dict, Optional, Set
from contextlib import asynccontextmanager

from app.services.cache import CacheService

logger = logging.getLogger(__name__)


class CacheContext:
    """Context manager for scoped cache operations with automatic cleanup."""
    
    def __init__(
        self,
        cache_service: Optional[CacheService] = None,
        cache_prefix: str = "",
        default_ttl: int = 300,
        auto_cleanup: bool = True
    ):
        self.cache_service = cache_service
        self.cache_prefix = cache_prefix
        self.default_ttl = default_ttl
        self.auto_cleanup = auto_cleanup
        
        # Track keys for cleanup
        self._tracked_keys: Set[str] = set()
        self._namespace_keys: Set[str] = set()
    
    async def __aenter__(self) -> "CacheContext":
        """Enter the cache context."""
        logger.debug(f"Starting cache context with prefix: {self.cache_prefix}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the cache context with optional cleanup."""
        if self.auto_cleanup and self.cache_service:
            await self._cleanup()
        
        logger.debug(f"Cache context cleanup completed for prefix: {self.cache_prefix}")
        return False  # Don't suppress exceptions
    
    def _make_key(self, key: str) -> str:
        """Create a prefixed cache key."""
        if self.cache_prefix:
            return f"{self.cache_prefix}:{key}"
        return key
    
    async def get(self, key: str) -> Any:
        """Get a value from cache."""
        if not self.cache_service:
            return None
        
        full_key = self._make_key(key)
        try:
            return await self.cache_service.get(full_key)
        except Exception as e:
            logger.warning(f"Cache get failed for key '{full_key}': {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set a value in cache and track the key."""
        if not self.cache_service:
            return False
        
        full_key = self._make_key(key)
        ttl = ttl or self.default_ttl
        
        try:
            success = await self.cache_service.set(full_key, value, ttl)
            if success:
                self._tracked_keys.add(full_key)
            return success
        except Exception as e:
            logger.warning(f"Cache set failed for key '{full_key}': {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete a value from cache."""
        if not self.cache_service:
            return False
        
        full_key = self._make_key(key)
        try:
            success = await self.cache_service.delete(full_key)
            self._tracked_keys.discard(full_key)
            return success
        except Exception as e:
            logger.warning(f"Cache delete failed for key '{full_key}': {e}")
            return False
    
    async def get_or_set(
        self,
        key: str,
        factory_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """Get a value from cache or set it using the factory function."""
        # Try to get from cache first
        value = await self.get(key)
        if value is not None:
            return value
        
        # Generate value using factory function
        try:
            if asyncio.iscoroutinefunction(factory_func):
                value = await factory_func()
            else:
                value = factory_func()
            
            # Store in cache
            await self.set(key, value, ttl)
            return value
            
        except Exception as e:
            logger.error(f"Factory function failed for cache key '{key}': {e}")
            raise
    
    async def set_namespace(self, namespace: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values under a namespace."""
        success_count = 0
        ttl = ttl or self.default_ttl
        
        for sub_key, value in data.items():
            full_key = self._make_key(f"{namespace}:{sub_key}")
            if await self.set(sub_key, value, ttl):
                self._namespace_keys.add(full_key)
                success_count += 1
        
        return success_count == len(data)
    
    async def get_namespace(self, namespace: str) -> Dict[str, Any]:
        """Get all values under a namespace."""
        # Note: This is a simplified implementation
        # In practice, you might want to use Redis SCAN or similar
        result = {}
        for key in list(self._tracked_keys):
            if key.startswith(self._make_key(f"{namespace}:")):
                sub_key = key.split(":", 2)[-1]  # Get the part after namespace
                value = await self.get(sub_key)
                if value is not None:
                    result[sub_key] = value
        
        return result
    
    async def clear_namespace(self, namespace: str) -> int:
        """Clear all values under a namespace."""
        cleared_count = 0
        keys_to_remove = []
        
        for key in list(self._tracked_keys):
            if key.startswith(self._make_key(f"{namespace}:")):
                if await self.cache_service.delete(key):
                    cleared_count += 1
                keys_to_remove.append(key)
        
        # Remove from tracking
        for key in keys_to_remove:
            self._tracked_keys.discard(key)
            self._namespace_keys.discard(key)
        
        return cleared_count
    
    async def _cleanup(self):
        """Cleanup all tracked cache keys."""
        if not self.cache_service or not self._tracked_keys:
            return
        
        cleanup_count = 0
        for key in list(self._tracked_keys):
            try:
                if await self.cache_service.delete(key):
                    cleanup_count += 1
            except Exception as e:
                logger.warning(f"Failed to cleanup cache key '{key}': {e}")
        
        self._tracked_keys.clear()
        self._namespace_keys.clear()
        
        if cleanup_count > 0:
            logger.debug(f"Cleaned up {cleanup_count} cache keys with prefix: {self.cache_prefix}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache context statistics."""
        return {
            "cache_prefix": self.cache_prefix,
            "tracked_keys_count": len(self._tracked_keys),
            "namespace_keys_count": len(self._namespace_keys),
            "auto_cleanup": self.auto_cleanup,
            "default_ttl": self.default_ttl
        }


@asynccontextmanager
async def cache_scope(
    cache_service: CacheService,
    scope_name: str,
    ttl: int = 300,
    auto_cleanup: bool = True
):
    """
    Create a scoped cache context.
    
    Usage:
        async with cache_scope(cache_service, "workflow_123") as cache:
            await cache.set("result", data)
            value = await cache.get("result")
    """
    context = CacheContext(
        cache_service=cache_service,
        cache_prefix=scope_name,
        default_ttl=ttl,
        auto_cleanup=auto_cleanup
    )
    async with context:
        yield context


import asyncio
