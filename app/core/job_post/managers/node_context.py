"""
Node context manager for managing individual node execution lifecycle.

Provides automatic resource management, timeout handling, and cleanup
for individual node operations within workflows.
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional, Callable
from contextlib import asynccontextmanager

from app.core.job_post.factory.workflow_config import WorkflowSettings
from app.core.job_post.factory.node_factory import NodeServices

logger = logging.getLogger(__name__)


class NodeContext:
    """Context manager for node execution with automatic resource management."""
    
    def __init__(
        self,
        node_name: str,
        services: NodeServices,
        settings: WorkflowSettings,
        timeout: Optional[int] = None
    ):
        self.node_name = node_name
        self.services = services
        self.settings = settings
        self.timeout = timeout or settings.node_timeout
        
        # Execution tracking
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.error: Optional[Exception] = None
        self.result: Any = None
        
        # Resource tracking
        self._temp_files: list[str] = []
        self._cache_keys: list[str] = []
        
    async def __aenter__(self) -> "NodeContext":
        """Enter the node context."""
        self.start_time = time.time()
        logger.debug(f"Starting node execution: {self.node_name}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the node context with cleanup."""
        self.end_time = time.time()
        duration = self.end_time - (self.start_time or self.end_time)
        
        if exc_type:
            self.error = exc_val
            logger.warning(
                f"Node {self.node_name} failed after {duration:.3f}s: {exc_val}"
            )
        else:
            logger.debug(
                f"Node {self.node_name} completed successfully in {duration:.3f}s"
            )
        
        await self._cleanup()
        return False  # Don't suppress exceptions
    
    async def execute_with_timeout(
        self,
        node_func: Callable,
        state: Any
    ) -> Any:
        """
        Execute a node function with timeout and resource tracking.
        
        Args:
            node_func: The node function to execute
            state: The workflow state
            
        Returns:
            Node execution result
        """
        try:
            # Execute with timeout
            self.result = await asyncio.wait_for(
                node_func(state),
                timeout=self.timeout
            )
            return self.result
            
        except asyncio.TimeoutError:
            logger.error(f"Node {self.node_name} timed out after {self.timeout}s")
            raise
        except Exception as e:
            logger.error(f"Node {self.node_name} execution failed: {e}")
            raise
    
    async def cache_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set a value in cache and track the key for cleanup."""
        full_key = f"node_{self.node_name}_{key}"
        self._cache_keys.append(full_key)
        return await self.services.cache_set(full_key, value, ttl)
    
    async def cache_get(self, key: str) -> Any:
        """Get a value from cache."""
        full_key = f"node_{self.node_name}_{key}"
        return await self.services.cache_get(full_key)
    
    def track_temp_file(self, file_path: str) -> None:
        """Track a temporary file for cleanup."""
        self._temp_files.append(file_path)
    
    async def _cleanup(self):
        """Cleanup node resources."""
        # Cleanup temporary files
        import os
        for file_path in self._temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
        
        # Note: We don't cleanup cache keys here as they might be needed
        # by other parts of the workflow. Cache cleanup is handled by TTL.
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics for the node."""
        duration = None
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
        
        return {
            "node_name": self.node_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration_seconds": duration,
            "timeout_seconds": self.timeout,
            "success": self.error is None,
            "error": str(self.error) if self.error else None,
            "temp_files_created": len(self._temp_files),
            "cache_keys_created": len(self._cache_keys)
        }


@asynccontextmanager
async def node_execution(
    node_name: str,
    services: NodeServices,
    settings: WorkflowSettings,
    timeout: Optional[int] = None
):
    """
    Async context manager for node execution.
    
    Usage:
        async with node_execution("parse_job_info", services, settings) as ctx:
            result = await ctx.execute_with_timeout(node_func, state)
    """
    context = NodeContext(node_name, services, settings, timeout)
    async with context:
        yield context


class NodeExecutionPool:
    """Pool for managing concurrent node executions with limits."""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_nodes: Dict[str, NodeContext] = {}
        self._lock = asyncio.Lock()
    
    @asynccontextmanager
    async def execute_node(
        self,
        node_name: str,
        services: NodeServices,
        settings: WorkflowSettings,
        timeout: Optional[int] = None
    ):
        """Execute a node with concurrency limits."""
        async with self.semaphore:
            async with self._lock:
                if node_name in self.active_nodes:
                    raise RuntimeError(f"Node {node_name} is already executing")
            
            async with node_execution(node_name, services, settings, timeout) as ctx:
                async with self._lock:
                    self.active_nodes[node_name] = ctx
                
                try:
                    yield ctx
                finally:
                    async with self._lock:
                        self.active_nodes.pop(node_name, None)
    
    async def get_active_nodes(self) -> Dict[str, Dict[str, Any]]:
        """Get information about currently active nodes."""
        async with self._lock:
            return {
                name: ctx.get_execution_stats()
                for name, ctx in self.active_nodes.items()
            }
    
    async def cancel_all(self) -> None:
        """Cancel all active node executions."""
        async with self._lock:
            active_count = len(self.active_nodes)
            self.active_nodes.clear()
        
        if active_count > 0:
            logger.warning(f"Cancelled {active_count} active node executions")
