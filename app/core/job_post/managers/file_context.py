"""
File context manager for managing temporary file operations with automatic cleanup.

Provides scoped file management with automatic cleanup of temporary files
and directories created during workflow execution.
"""

import asyncio
import logging
import os
import shutil
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class FileContext:
    """Context manager for temporary file operations with automatic cleanup."""
    
    def __init__(
        self,
        base_path: Optional[str] = None,
        auto_cleanup: bool = True,
        create_base_dir: bool = True
    ):
        self.base_path = Path(base_path) if base_path else None
        self.auto_cleanup = auto_cleanup
        self.create_base_dir = create_base_dir
        
        # Track created files and directories
        self._temp_files: List[Path] = []
        self._temp_dirs: List[Path] = []
        self._created_base_dir = False
    
    async def __aenter__(self) -> "FileContext":
        """Enter the file context."""
        if self.base_path and self.create_base_dir:
            try:
                self.base_path.mkdir(parents=True, exist_ok=True)
                self._created_base_dir = True
                logger.debug(f"Created base directory: {self.base_path}")
            except Exception as e:
                logger.error(f"Failed to create base directory {self.base_path}: {e}")
                raise
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the file context with cleanup."""
        if self.auto_cleanup:
            await self._cleanup()
        
        logger.debug(f"File context cleanup completed")
        return False  # Don't suppress exceptions
    
    def create_temp_file(
        self,
        suffix: str = "",
        prefix: str = "tmp",
        content: Optional[Union[str, bytes]] = None,
        encoding: str = "utf-8"
    ) -> Path:
        """
        Create a temporary file and track it for cleanup.
        
        Args:
            suffix: File suffix (e.g., '.txt', '.json')
            prefix: File prefix
            content: Optional content to write to the file
            encoding: Text encoding for string content
            
        Returns:
            Path to the created temporary file
        """
        try:
            # Create temp file in base directory if specified
            dir_path = self.base_path if self.base_path else None
            
            fd, temp_path = tempfile.mkstemp(
                suffix=suffix,
                prefix=prefix,
                dir=dir_path
            )
            
            temp_file = Path(temp_path)
            self._temp_files.append(temp_file)
            
            # Write content if provided
            if content is not None:
                with os.fdopen(fd, 'wb' if isinstance(content, bytes) else 'w', encoding=encoding if isinstance(content, str) else None) as f:
                    f.write(content)
            else:
                os.close(fd)
            
            logger.debug(f"Created temp file: {temp_file}")
            return temp_file
            
        except Exception as e:
            logger.error(f"Failed to create temp file: {e}")
            raise
    
    def create_temp_dir(self, prefix: str = "tmpdir") -> Path:
        """
        Create a temporary directory and track it for cleanup.
        
        Args:
            prefix: Directory prefix
            
        Returns:
            Path to the created temporary directory
        """
        try:
            # Create temp directory in base directory if specified
            dir_path = self.base_path if self.base_path else None
            
            temp_dir = Path(tempfile.mkdtemp(prefix=prefix, dir=dir_path))
            self._temp_dirs.append(temp_dir)
            
            logger.debug(f"Created temp directory: {temp_dir}")
            return temp_dir
            
        except Exception as e:
            logger.error(f"Failed to create temp directory: {e}")
            raise
    
    def write_file(
        self,
        filename: str,
        content: Union[str, bytes],
        encoding: str = "utf-8",
        create_dirs: bool = True
    ) -> Path:
        """
        Write content to a file in the base directory.
        
        Args:
            filename: Name of the file to create
            content: Content to write
            encoding: Text encoding for string content
            create_dirs: Whether to create parent directories
            
        Returns:
            Path to the created file
        """
        try:
            if self.base_path:
                file_path = self.base_path / filename
            else:
                file_path = Path(filename)
            
            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content
            if isinstance(content, bytes):
                file_path.write_bytes(content)
            else:
                file_path.write_text(content, encoding=encoding)
            
            self._temp_files.append(file_path)
            logger.debug(f"Created file: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to write file {filename}: {e}")
            raise
    
    def read_file(
        self,
        file_path: Union[str, Path],
        encoding: str = "utf-8",
        binary: bool = False
    ) -> Union[str, bytes]:
        """
        Read content from a file.
        
        Args:
            file_path: Path to the file to read
            encoding: Text encoding for text files
            binary: Whether to read as binary
            
        Returns:
            File content as string or bytes
        """
        try:
            path = Path(file_path)
            
            if binary:
                return path.read_bytes()
            else:
                return path.read_text(encoding=encoding)
                
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise
    
    def copy_file(self, src: Union[str, Path], dst: Union[str, Path]) -> Path:
        """
        Copy a file and track the destination for cleanup.
        
        Args:
            src: Source file path
            dst: Destination file path
            
        Returns:
            Path to the copied file
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            # Create destination directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(src_path, dst_path)
            self._temp_files.append(dst_path)
            
            logger.debug(f"Copied file from {src_path} to {dst_path}")
            return dst_path
            
        except Exception as e:
            logger.error(f"Failed to copy file from {src} to {dst}: {e}")
            raise
    
    def list_files(self, pattern: str = "*") -> List[Path]:
        """
        List files in the base directory matching a pattern.
        
        Args:
            pattern: Glob pattern to match files
            
        Returns:
            List of matching file paths
        """
        if not self.base_path or not self.base_path.exists():
            return []
        
        try:
            return list(self.base_path.glob(pattern))
        except Exception as e:
            logger.error(f"Failed to list files with pattern {pattern}: {e}")
            return []
    
    async def _cleanup(self):
        """Cleanup all tracked files and directories."""
        cleanup_count = 0
        
        # Remove files first
        for file_path in self._temp_files:
            try:
                if file_path.exists():
                    file_path.unlink()
                    cleanup_count += 1
                    logger.debug(f"Removed temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to remove temp file {file_path}: {e}")
        
        # Remove directories
        for dir_path in self._temp_dirs:
            try:
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    cleanup_count += 1
                    logger.debug(f"Removed temp directory: {dir_path}")
            except Exception as e:
                logger.warning(f"Failed to remove temp directory {dir_path}: {e}")
        
        # Remove base directory if we created it and it's empty
        if (self._created_base_dir and 
            self.base_path and 
            self.base_path.exists() and 
            not any(self.base_path.iterdir())):
            try:
                self.base_path.rmdir()
                logger.debug(f"Removed empty base directory: {self.base_path}")
            except Exception as e:
                logger.warning(f"Failed to remove base directory {self.base_path}: {e}")
        
        self._temp_files.clear()
        self._temp_dirs.clear()
        
        if cleanup_count > 0:
            logger.debug(f"Cleaned up {cleanup_count} files/directories")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get file context statistics."""
        return {
            "base_path": str(self.base_path) if self.base_path else None,
            "temp_files_count": len(self._temp_files),
            "temp_dirs_count": len(self._temp_dirs),
            "auto_cleanup": self.auto_cleanup,
            "created_base_dir": self._created_base_dir
        }


@asynccontextmanager
async def temp_file_scope(
    base_path: Optional[str] = None,
    auto_cleanup: bool = True
):
    """
    Create a temporary file scope context.
    
    Usage:
        async with temp_file_scope("/tmp/workflow") as files:
            temp_file = files.create_temp_file(suffix=".json", content='{"test": true}')
            content = files.read_file(temp_file)
    """
    context = FileContext(
        base_path=base_path,
        auto_cleanup=auto_cleanup
    )
    async with context:
        yield context
