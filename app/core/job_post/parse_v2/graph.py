"""
Parse V2 workflow graph using the new factory system.

This module provides the migrated parse_v2 workflow that uses the new
graph factory, dependency injection, and context managers.
"""

from langgraph.graph import CompiledGraph

from app.core.job_post.factory import GraphFactory, WorkflowType
from app.core.job_post.parse.state import (
    ParseJobPostInput,
    ParseJobPostOutput,
    ParseJobPostState,
)


async def get_parse_v2_graph_new(graph_factory: GraphFactory) -> CompiledGraph:
    """
    Get parse v2 graph using the new factory system.

    Args:
        graph_factory: Graph factory instance with dependency injection

    Returns:
        Compiled graph for parse v2 workflow
    """
    return await graph_factory.get_graph(
        WorkflowType.PARSE_V2, ParseJobPostState, ParseJobPostInput, ParseJobPostOutput
    )


# Legacy function for backward compatibility
# TODO: Remove this once all routes are migrated
def get_parse_v2_graph():
    """
    Legacy function for backward compatibility.

    This function maintains the old interface but will be removed
    once all routes are migrated to the new system.
    """
    from langgraph.graph import END, START, StateGraph
    from langgraph.pregel.main import RetryPolicy

    from app.core.job_post.parse.nodes import (
        parse_job_extra_info,
        parse_job_info,
        postprocess,
        suggest_job_function,
        suggest_skills,
        validate_content,
    )

    def should_continue(state: ParseJobPostState):
        if not state.is_valid_content:
            return END
        return ["parse_job_info", "parse_job_extra_info"]

    # Global cached graph instance
    global _compiled_graph

    if "_compiled_graph" not in globals() or _compiled_graph is None:
        workflow = StateGraph(
            ParseJobPostState, input=ParseJobPostInput, output=ParseJobPostOutput
        )

        retry = RetryPolicy(max_attempts=3)

        workflow.add_node("validate_content", validate_content)
        workflow.add_node("parse_job_info", parse_job_info, retry=retry)
        workflow.add_node("parse_job_extra_info", parse_job_extra_info, retry=retry)
        workflow.add_node("suggest_skills", suggest_skills)
        workflow.add_node("suggest_job_function", suggest_job_function)
        workflow.add_node("postprocess", postprocess)

        workflow.add_edge(START, "validate_content")
        workflow.add_conditional_edges("validate_content", should_continue)
        workflow.add_edge("parse_job_info", "suggest_skills")
        workflow.add_edge("parse_job_info", "suggest_job_function")
        workflow.add_edge(
            [
                "parse_job_extra_info",
                "suggest_skills",
                "suggest_job_function",
            ],
            "postprocess",
        )
        workflow.add_edge("postprocess", END)

        _compiled_graph = workflow.compile()
        _compiled_graph.name = "Job Post Parse V2"

    return _compiled_graph


# Global variable for legacy compatibility
_compiled_graph = None
