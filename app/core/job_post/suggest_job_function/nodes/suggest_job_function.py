from enum import Enum
from typing import List

from pydantic import Field, create_model

from app.core.job_post.suggest_job_function.prompt import SUGGEST_JOB_FUNCTION_PROMPT
from app.core.job_post.suggest_job_function.state import SuggestJobFunctionState
from app.container import get_service_container


def postprocess_job_function(job_function: dict, job_function_list: list):
    response = {}
    job_function_id_enum = {
        job_function["nameEn"]: job_function["id"] for job_function in job_function_list
    }
    JobFunctionIdEnum = Enum("JobFunctionIdEnum", job_function_id_enum)

    most_related_job_function = job_function.get("most_related_job_function")
    if most_related_job_function:
        response["most_related_job_function"] = {
            "job_function": most_related_job_function,
            "job_function_id": JobFunctionIdEnum[most_related_job_function].value,
        }
    list_related_job_function = job_function.get("related_job_function", [])
    for related_job_function in list_related_job_function:
        if "related_job_function" not in response:
            response["related_job_function"] = [
                {
                    "job_function": related_job_function,
                    "job_function_id": JobFunctionIdEnum[related_job_function].value,
                }
            ]
        else:
            response["related_job_function"].append(
                {
                    "job_function": related_job_function,
                    "job_function_id": JobFunctionIdEnum[related_job_function].value,
                }
            )
    return response


async def suggest_job_function(state: SuggestJobFunctionState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0)
    user_prompt = f"Job title: {state.job_title}"
    messages = [
        {"role": "system", "content": SUGGEST_JOB_FUNCTION_PROMPT},
        {
            "role": "user",
            "content": user_prompt,
        },
    ]

    # Create job function model JobFunction
    job_function_list = state.list_job_function
    job_function_enum = {
        job_function["nameEn"]: job_function["nameEn"]
        for job_function in job_function_list
    }
    JobFunctionEnum = Enum("JobFunctionEnum", job_function_enum)

    SuggestJobFunction = create_model(
        "SuggestJobFunction",
        most_related_job_function=(
            JobFunctionEnum,
            Field(
                ...,
                description="Most related job function in predefined list.",
            ),
        ),
        related_job_function=(
            List[JobFunctionEnum],
            Field(..., description="List of related job function in predefined."),
        ),
    )
    chain = llm.with_structured_output(SuggestJobFunction)
    response = await chain.ainvoke(messages)
    job_function = response.model_dump(mode="json")
    response_job_function = postprocess_job_function(job_function, job_function_list)
    return {
        "most_related_job_function": response_job_function["most_related_job_function"],
        "related_job_function": response_job_function["related_job_function"],
    }
