from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_job_function.nodes import suggest_job_function
from app.core.job_post.suggest_job_function.state import (
    SuggestJobFunctionInput,
    SuggestJobFunctionState,
)


# Global cached graph instance
_compiled_graph = None


def get_suggest_job_function_graph():
    """Get cached suggest job function graph to prevent creating new graphs for each request."""
    global _compiled_graph

    if _compiled_graph is None:
        workflow = StateGraph(
            SuggestJobFunctionState,
            input=SuggestJobFunctionInput,
            output=SuggestJobFunctionState,
        )

        workflow.add_node("suggest_job_function", suggest_job_function)

        workflow.add_edge(START, "suggest_job_function")
        workflow.add_edge("suggest_job_function", END)

        _compiled_graph = workflow.compile()
        _compiled_graph.name = "Job Post Suggest Job Function"

    return _compiled_graph
