"""
Model cache service for Pydantic model generation and caching.

Provides specialized caching for dynamically generated Pydantic models
based on metadata, replacing the existing TTL cache implementations.
"""

import json
import logging
from typing import Any, Dict, Type, Optional
from enum import Enum

from pydantic import BaseModel, Field, create_model

from .unified_cache import UnifiedCacheService

logger = logging.getLogger(__name__)


class ModelCacheService:
    """Service for caching dynamically generated Pydantic models."""
    
    def __init__(self, cache_service: UnifiedCacheService):
        self.cache_service = cache_service
    
    async def get_job_info_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """
        Get or create a JobInfo model based on metadata.
        
        Args:
            metadata: Metadata containing job information structure
            
        Returns:
            Dynamically created JobInfo model class
        """
        return await self.cache_service.cache_model(
            "job_info",
            metadata,
            lambda: self._create_job_info_model(metadata)
        )
    
    async def get_job_extra_info_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """
        Get or create a JobExtraInfo model based on metadata.
        
        Args:
            metadata: Metadata containing job extra information structure
            
        Returns:
            Dynamically created JobExtraInfo model class
        """
        return await self.cache_service.cache_model(
            "job_extra_info",
            metadata,
            lambda: self._create_job_extra_info_model(metadata)
        )
    
    async def get_generate_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """
        Get or create a Job generation model based on metadata.
        
        Args:
            metadata: Metadata containing job generation structure
            
        Returns:
            Dynamically created Job model class
        """
        return await self.cache_service.cache_model(
            "generate_job",
            metadata,
            lambda: self._create_generate_model(metadata)
        )
    
    def _create_job_info_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """Create JobInfo model from metadata."""
        # Import the original function to maintain compatibility
        from app.core.job_post.parse.model import _create_job_info_model
        return _create_job_info_model(metadata)
    
    def _create_job_extra_info_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """Create JobExtraInfo model from metadata."""
        # Import the original function to maintain compatibility
        from app.core.job_post.parse.model import _create_job_extra_info_model
        return _create_job_extra_info_model(metadata)
    
    def _create_generate_model(self, metadata: Dict[str, Any]) -> Type[BaseModel]:
        """Create Job generation model from metadata."""
        # Import the original function to maintain compatibility
        from app.core.job_post.generate_v2.job_post_model import _create_cached_generate_model
        
        # Create a hash of the metadata for the function
        metadata_str = json.dumps(metadata, sort_keys=True)
        metadata_hash = self.cache_service._hash_content(metadata_str)
        
        return _create_cached_generate_model(metadata_hash, metadata_str)
    
    async def clear_model_cache(self) -> int:
        """Clear all cached models."""
        cleared = 0
        for model_type in ["job_info", "job_extra_info", "generate_job"]:
            cleared += await self.cache_service.clear_category(f"models:{model_type}")
        return cleared
    
    def get_stats(self) -> Dict[str, Any]:
        """Get model cache statistics."""
        return {
            "service_type": "model_cache",
            **self.cache_service.get_stats()
        }


# Compatibility functions for existing code
async def generate_model_job_info(metadata: Dict[str, Any]) -> Type[BaseModel]:
    """
    Compatibility function for existing code.
    
    This function maintains the same interface as the original
    but uses the new caching system.
    """
    # This would need to be injected in practice
    # For now, we'll create a temporary service
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        # Would need proper settings injection
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    model_cache = ModelCacheService(unified_cache)
    return await model_cache.get_job_info_model(metadata)


async def generate_model_job_extra_info(metadata: Dict[str, Any]) -> Type[BaseModel]:
    """
    Compatibility function for existing code.
    
    This function maintains the same interface as the original
    but uses the new caching system.
    """
    # This would need to be injected in practice
    # For now, we'll create a temporary service
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        # Would need proper settings injection
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    model_cache = ModelCacheService(unified_cache)
    return await model_cache.get_job_extra_info_model(metadata)


async def create_generate_model(metadata: Dict[str, Any]) -> Type[BaseModel]:
    """
    Compatibility function for existing code.
    
    This function maintains the same interface as the original
    but uses the new caching system.
    """
    # This would need to be injected in practice
    # For now, we'll create a temporary service
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        # Would need proper settings injection
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    model_cache = ModelCacheService(unified_cache)
    return await model_cache.get_generate_model(metadata)
