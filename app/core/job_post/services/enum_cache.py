"""
Enum cache service for dynamic enum generation and caching.

Provides specialized caching for dynamically generated enums
based on metadata, replacing the existing TTL cache implementations.
"""

import json
import logging
from typing import Any, Dict, List, Type, Optional
from enum import Enum

from .unified_cache import UnifiedCacheService

logger = logging.getLogger(__name__)


class EnumCacheService:
    """Service for caching dynamically generated enums."""
    
    def __init__(self, cache_service: UnifiedCacheService):
        self.cache_service = cache_service
    
    async def get_benefit_enum(self, benefits: List[Dict[str, Any]]) -> Type[Enum]:
        """
        Get or create a BenefitIdEnum based on benefits data.
        
        Args:
            benefits: List of benefit dictionaries
            
        Returns:
            Dynamically created BenefitIdEnum class
        """
        return await self.cache_service.cache_enum(
            "benefit_id",
            benefits,
            lambda: self._create_benefit_enum(benefits)
        )
    
    async def get_city_enum(self, cities: List[Dict[str, Any]]) -> Type[Enum]:
        """
        Get or create a CityEnum based on cities data.
        
        Args:
            cities: List of city dictionaries
            
        Returns:
            Dynamically created CityEnum class
        """
        return await self.cache_service.cache_enum(
            "city",
            cities,
            lambda: self._create_city_enum(cities)
        )
    
    async def get_district_enum(self, districts: List[Dict[str, Any]]) -> Type[Enum]:
        """
        Get or create a DistrictEnum based on districts data.
        
        Args:
            districts: List of district dictionaries
            
        Returns:
            Dynamically created DistrictEnum class
        """
        return await self.cache_service.cache_enum(
            "district",
            districts,
            lambda: self._create_district_enum(districts)
        )
    
    async def get_job_function_enum(self, job_functions: List[Dict[str, Any]]) -> Type[Enum]:
        """
        Get or create a JobFunctionEnum based on job functions data.
        
        Args:
            job_functions: List of job function dictionaries
            
        Returns:
            Dynamically created JobFunctionEnum class
        """
        return await self.cache_service.cache_enum(
            "job_function",
            job_functions,
            lambda: self._create_job_function_enum(job_functions)
        )
    
    async def get_skill_enum(self, skills: List[Dict[str, Any]]) -> Type[Enum]:
        """
        Get or create a SkillEnum based on skills data.
        
        Args:
            skills: List of skill dictionaries
            
        Returns:
            Dynamically created SkillEnum class
        """
        return await self.cache_service.cache_enum(
            "skill",
            skills,
            lambda: self._create_skill_enum(skills)
        )
    
    def _create_benefit_enum(self, benefits: List[Dict[str, Any]]) -> Type[Enum]:
        """Create BenefitIdEnum from benefits data."""
        benefit_enum = {
            benefit["benefitType"]: benefit["id"] 
            for benefit in benefits
        }
        return Enum("BenefitIdEnum", benefit_enum)
    
    def _create_city_enum(self, cities: List[Dict[str, Any]]) -> Type[Enum]:
        """Create CityEnum from cities data."""
        city_enum = {
            city["nameEn"]: city["nameEn"] 
            for city in cities
        }
        return Enum("CityEnum", city_enum)
    
    def _create_district_enum(self, districts: List[Dict[str, Any]]) -> Type[Enum]:
        """Create DistrictEnum from districts data."""
        district_enum = {
            district["nameEn"]: district["nameEn"] 
            for district in districts
        }
        return Enum("DistrictEnum", district_enum)
    
    def _create_job_function_enum(self, job_functions: List[Dict[str, Any]]) -> Type[Enum]:
        """Create JobFunctionEnum from job functions data."""
        job_function_enum = {
            jf["nameEn"]: jf["nameEn"] 
            for jf in job_functions
        }
        return Enum("JobFunctionEnum", job_function_enum)
    
    def _create_skill_enum(self, skills: List[Dict[str, Any]]) -> Type[Enum]:
        """Create SkillEnum from skills data."""
        skill_enum = {
            skill["nameEn"]: skill["nameEn"] 
            for skill in skills
        }
        return Enum("SkillEnum", skill_enum)
    
    async def clear_enum_cache(self) -> int:
        """Clear all cached enums."""
        cleared = 0
        for enum_type in ["benefit_id", "city", "district", "job_function", "skill"]:
            cleared += await self.cache_service.clear_category(f"enums:{enum_type}")
        return cleared
    
    def get_stats(self) -> Dict[str, Any]:
        """Get enum cache statistics."""
        return {
            "service_type": "enum_cache",
            **self.cache_service.get_stats()
        }


# Compatibility functions for existing code
async def get_cached_benefit_enum(benefits: List[Dict[str, Any]]) -> Type[Enum]:
    """
    Compatibility function for existing code.
    
    This function maintains the same interface as the original
    but uses the new caching system.
    """
    # This would need to be injected in practice
    # For now, we'll create a temporary service
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        # Would need proper settings injection
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    enum_cache = EnumCacheService(unified_cache)
    return await enum_cache.get_benefit_enum(benefits)


async def get_cached_city_enum(cities: List[Dict[str, Any]]) -> Type[Enum]:
    """
    Compatibility function for existing code.
    """
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    enum_cache = EnumCacheService(unified_cache)
    return await enum_cache.get_city_enum(cities)


async def get_cached_district_enum(districts: List[Dict[str, Any]]) -> Type[Enum]:
    """
    Compatibility function for existing code.
    """
    from app.container import get_service_container
    from .unified_cache import JobPostCacheService
    
    container = get_service_container()
    unified_cache = JobPostCacheService(
        container.cache_service,
        type('Settings', (), {'cache_ttl': 300})()
    )
    
    enum_cache = EnumCacheService(unified_cache)
    return await enum_cache.get_district_enum(districts)
