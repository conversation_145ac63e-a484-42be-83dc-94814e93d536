"""
Unified cache service for job post workflows.

Replaces multiple TTL caches with a single Redis-backed cache service
with configurable limits, cleanup policies, and content-based keys.
"""

import hashlib
import json
import logging
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass

from app.services.cache import CacheService
from app.core.job_post.factory.workflow_config import WorkflowSettings

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """Statistics for cache operations."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class UnifiedCacheService:
    """Unified cache service for job post workflows with Redis backend."""
    
    def __init__(
        self,
        cache_service: CacheService,
        settings: WorkflowSettings,
        cache_prefix: str = "job_post"
    ):
        self.cache_service = cache_service
        self.settings = settings
        self.cache_prefix = cache_prefix
        self.stats = CacheStats()
    
    def _make_key(self, category: str, key: str) -> str:
        """Create a prefixed cache key."""
        return f"{self.cache_prefix}:{category}:{key}"
    
    def _hash_content(self, content: Union[str, Dict, List]) -> str:
        """Create a hash of content for cache keys."""
        if isinstance(content, (dict, list)):
            content_str = json.dumps(content, sort_keys=True, separators=(',', ':'))
        else:
            content_str = str(content)
        
        return hashlib.sha256(content_str.encode()).hexdigest()[:16]
    
    async def get(self, category: str, key: str) -> Any:
        """
        Get a value from cache.
        
        Args:
            category: Cache category (e.g., 'enums', 'models', 'metadata')
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        cache_key = self._make_key(category, key)
        
        try:
            value = await self.cache_service.get(cache_key)
            if value is not None:
                self.stats.hits += 1
                logger.debug(f"Cache hit for {category}:{key}")
            else:
                self.stats.misses += 1
                logger.debug(f"Cache miss for {category}:{key}")
            return value
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache get error for {category}:{key}: {e}")
            return None
    
    async def set(
        self,
        category: str,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """
        Set a value in cache.
        
        Args:
            category: Cache category
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if None)
            
        Returns:
            True if successful, False otherwise
        """
        cache_key = self._make_key(category, key)
        ttl = ttl or self.settings.cache_ttl
        
        try:
            success = await self.cache_service.set(cache_key, value, ttl)
            if success:
                self.stats.sets += 1
                logger.debug(f"Cache set for {category}:{key} (ttl: {ttl}s)")
            return success
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache set error for {category}:{key}: {e}")
            return False
    
    async def delete(self, category: str, key: str) -> bool:
        """
        Delete a value from cache.
        
        Args:
            category: Cache category
            key: Cache key
            
        Returns:
            True if successful, False otherwise
        """
        cache_key = self._make_key(category, key)
        
        try:
            success = await self.cache_service.delete(cache_key)
            if success:
                self.stats.deletes += 1
                logger.debug(f"Cache delete for {category}:{key}")
            return success
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache delete error for {category}:{key}: {e}")
            return False
    
    async def get_or_create(
        self,
        category: str,
        key: str,
        factory_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """
        Get a value from cache or create it using the factory function.
        
        Args:
            category: Cache category
            key: Cache key
            factory_func: Function to create the value if not cached
            ttl: Time to live in seconds
            
        Returns:
            Cached or newly created value
        """
        # Try to get from cache first
        value = await self.get(category, key)
        if value is not None:
            return value
        
        # Create value using factory function
        try:
            if asyncio.iscoroutinefunction(factory_func):
                value = await factory_func()
            else:
                value = factory_func()
            
            # Store in cache
            await self.set(category, key, value, ttl)
            return value
            
        except Exception as e:
            logger.error(f"Factory function failed for {category}:{key}: {e}")
            raise
    
    async def get_content_based(
        self,
        category: str,
        content: Union[str, Dict, List],
        factory_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """
        Get or create a cached value based on content hash.
        
        This is useful for caching generated models/enums based on metadata content.
        
        Args:
            category: Cache category
            content: Content to hash for the cache key
            factory_func: Function to create the value if not cached
            ttl: Time to live in seconds
            
        Returns:
            Cached or newly created value
        """
        content_hash = self._hash_content(content)
        return await self.get_or_create(category, content_hash, factory_func, ttl)
    
    async def clear_category(self, category: str) -> int:
        """
        Clear all cache entries for a specific category.
        
        Note: This is a simplified implementation. In production, you might
        want to use Redis SCAN with pattern matching for better performance.
        
        Args:
            category: Cache category to clear
            
        Returns:
            Number of entries cleared
        """
        # This is a placeholder implementation
        # In practice, you'd need to implement pattern-based deletion
        logger.warning(f"Clear category {category} not fully implemented")
        return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "hits": self.stats.hits,
            "misses": self.stats.misses,
            "sets": self.stats.sets,
            "deletes": self.stats.deletes,
            "errors": self.stats.errors,
            "hit_rate": self.stats.hit_rate,
            "cache_prefix": self.cache_prefix,
            "default_ttl": self.settings.cache_ttl
        }
    
    def reset_stats(self) -> None:
        """Reset cache statistics."""
        self.stats = CacheStats()


# Specialized cache methods for common job post operations
class JobPostCacheService(UnifiedCacheService):
    """Specialized cache service for job post operations."""
    
    async def cache_enum(
        self,
        enum_type: str,
        metadata: Dict[str, Any],
        factory_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """Cache an enum based on metadata."""
        return await self.get_content_based(
            f"enums:{enum_type}",
            metadata,
            factory_func,
            ttl
        )
    
    async def cache_model(
        self,
        model_type: str,
        metadata: Dict[str, Any],
        factory_func: callable,
        ttl: Optional[int] = None
    ) -> Any:
        """Cache a Pydantic model based on metadata."""
        return await self.get_content_based(
            f"models:{model_type}",
            metadata,
            factory_func,
            ttl
        )
    
    async def cache_location_data(
        self,
        location_type: str,
        data: Union[str, List[Dict]],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache location data (cities, districts, etc.)."""
        key = self._hash_content(data) if isinstance(data, list) else data
        return await self.set(f"location:{location_type}", key, data, ttl)
    
    async def get_location_data(
        self,
        location_type: str,
        data_key: Union[str, List[Dict]]
    ) -> Any:
        """Get cached location data."""
        key = self._hash_content(data_key) if isinstance(data_key, list) else data_key
        return await self.get(f"location:{location_type}", key)


import asyncio
