import logging
from enum import Enum
from typing import Optional

from cachetools import TTLCache
from pydantic import Field, create_model

from app.container import get_service_container
from app.core.utils import get_currency_exchange_rate
from app.helpers import get_district

logger = logging.getLogger(__name__)


_benefit_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def _create_cached_benefit_enum(benefit_hash: str, benefit_data: str):
    """Create cached benefit enum based on data hash"""
    # Check cache first
    cache_key = benefit_hash
    if cache_key in _benefit_cache:
        return _benefit_cache[cache_key]

    import json

    job_benefit_list = json.loads(benefit_data)

    job_benefit_id_enum = {
        job_benefit["nameEn"]: job_benefit["id"] for job_benefit in job_benefit_list
    }
    result = Enum("BenefitIdEnum", job_benefit_id_enum)
    _benefit_cache[cache_key] = result
    return result


def postprocess_benefits(benefits, job_benefit_list):
    import hashlib
    import json

    # Create hash for caching
    benefit_data = json.dumps(job_benefit_list, sort_keys=True)
    benefit_hash = hashlib.md5(benefit_data.encode(), usedforsecurity=False).hexdigest()

    BenefitIdEnum = _create_cached_benefit_enum(benefit_hash, benefit_data)

    benefit_groups = {}
    for benefit in benefits:
        benefit_type = benefit.get("benefit_type")
        benefit_description = benefit.get("benefit_description")
        if benefit_type in benefit_groups:
            benefit_groups[benefit_type].append(benefit_description)
        else:
            benefit_groups[benefit_type] = [benefit_description]

    new_benefits = []
    for benefit_type, benefit_descriptions in benefit_groups.items():
        concatenated = ". ".join(
            benefit_description.rstrip(".")
            for benefit_description in benefit_descriptions
        )
        new_benefits.append(
            {
                "benefit_id": BenefitIdEnum[benefit_type].value,
                "benefit_type": benefit_type,
                "benefit_description": concatenated[:500],
            }
        )
    return new_benefits


_city_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def _create_cached_city_enums_and_model(city_hash: str, city_data: str):
    """Create cached city enums and Province model based on data hash"""
    # Check cache first
    cache_key = city_hash
    if cache_key in _city_cache:
        return _city_cache[cache_key]

    import json

    city = json.loads(city_data)

    city_id_enum = {data["nameEn"]: data["id"] for data in city}
    CityIdEnum = Enum("CityIdEnum", city_id_enum)
    city_enum = {data["name"]: data["nameEn"] for data in city}
    ProvinceEnum = Enum("ProvinceEnum", city_enum)
    Province = create_model(
        "Province",
        province_name=(
            Optional[ProvinceEnum],
            Field(None, description="Name of the province"),
        ),
    )
    result = (CityIdEnum, ProvinceEnum, Province)
    _city_cache[cache_key] = result
    return result


_district_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def _create_cached_district_enums_and_model(district_hash: str, district_data: str):
    """Create cached district enums and District model based on data hash"""
    # Check cache first
    cache_key = district_hash
    if cache_key in _district_cache:
        return _district_cache[cache_key]

    import json

    district_list = json.loads(district_data)

    district_enum = {
        district["nameEn"]: district["nameEn"] for district in district_list
    }
    DistrictEnum = Enum("DistrictEnum", district_enum)

    district_id_enum = {
        district["nameEn"]: district["id"] for district in district_list
    }
    DistrictIdEnum = Enum("DistrictIdEnum", district_id_enum)

    District = create_model("District", district=(Optional[DistrictEnum], None))
    result = (DistrictEnum, DistrictIdEnum, District)
    _district_cache[cache_key] = result
    return result


async def postprocess_working_location(job_extra_info, city):
    working_location = (
        job_extra_info.get("working_location")
        if job_extra_info.get("working_location")
        else []
    )
    new_working_location = []

    import hashlib
    import json
    import gc

    # Create hash for caching city data
    city_data = json.dumps(city, sort_keys=True)
    city_hash = hashlib.md5(city_data.encode(), usedforsecurity=False).hexdigest()

    CityIdEnum, ProvinceEnum, Province = _create_cached_city_enums_and_model(
        city_hash, city_data
    )

    # Create single LLM client instance for reuse
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0)

    for data in working_location:
        # Handle enum values (convert to string if needed)
        address = data.get("full_address")
        try:
            chain = llm.with_structured_output(Province)
            response = await chain.ainvoke(address)
            city_name = response.province_name.value
        except Exception:
            city_name = None
        if city_name and city_name != "Other":
            if city_name:
                city_id = CityIdEnum[city_name].value
                district_list = await get_district(city_id)
                if district_list:
                    # Create hash for caching district data
                    district_data = json.dumps(district_list, sort_keys=True)
                    district_hash = hashlib.md5(
                        district_data.encode(), usedforsecurity=False
                    ).hexdigest()

                    DistrictEnum, DistrictIdEnum, District = (
                        _create_cached_district_enums_and_model(
                            district_hash, district_data
                        )
                    )
                    try:
                        chain = llm.with_structured_output(District)
                        response = await chain.ainvoke(address)
                        district = response.district
                    except Exception:
                        district = None
                    if district:
                        new_working_location.append(
                            {
                                "address": address,
                                "city_id": city_id,
                                "city": city_name,
                                "district_id": DistrictIdEnum[district.value].value,
                                "district": district.value,
                            }
                        )
                    else:
                        new_working_location.append(
                            {
                                "address": address,
                                "city_id": city_id,
                                "city": city_name,
                                "district_id": None,
                                "district": None,
                            }
                        )
                else:
                    new_working_location.append(
                        {
                            "address": address,
                            "city_id": city_id,
                            "city": city_name,
                            "district_id": None,
                            "district": None,
                        }
                    )
            else:
                new_working_location.append(
                    {
                        "address": address,
                        "city_id": None,
                        "city": None,
                        "district_id": None,
                        "district": None,
                    }
                )
        else:
            new_working_location.append(
                {
                    "address": address,
                    "city_id": None,
                    "city": None,
                    "district_id": None,
                    "district": None,
                }
            )

    # Force garbage collection after processing dynamic objects
    gc.collect()
    return new_working_location


_job_extra_info_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def clear_all_caches():
    """Clear all module-level caches to prevent memory leaks."""
    global _benefit_cache, _city_cache, _district_cache, _job_extra_info_cache

    cache_counts = {
        "benefit_cache": len(_benefit_cache),
        "city_cache": len(_city_cache),
        "district_cache": len(_district_cache),
        "job_extra_info_cache": len(_job_extra_info_cache),
    }

    _benefit_cache.clear()
    _city_cache.clear()
    _district_cache.clear()
    _job_extra_info_cache.clear()

    logger.info(f"Cleared caches: {cache_counts}")


def _create_cached_job_extra_info_enums(metadata_hash: str, metadata_str: str):
    """Create cached job extra info enums based on metadata hash"""
    # Check cache first
    cache_key = metadata_hash
    if cache_key in _job_extra_info_cache:
        return _job_extra_info_cache[cache_key]

    import json

    metadata = json.loads(metadata_str)

    # Create all the enums we need
    enums = {}

    # Salary payment period
    salary_payment_period_list = metadata["salaryPaymentPeriod"]
    salary_payment_period_id_enum = {
        salary_payment_period["nameEn"]: salary_payment_period["id"]
        for salary_payment_period in salary_payment_period_list
    }
    enums["salary_payment_period"] = Enum(
        "SalaryPaymentPeriodIdEnum", salary_payment_period_id_enum
    )

    # Salary currency
    salary_currency_list = metadata["salaryCurrency"]
    salary_currency_id_enum = {
        salary_currency["nameEn"]: salary_currency["id"]
        for salary_currency in salary_currency_list
    }
    enums["salary_currency"] = Enum("SalaryCurrencyIdEnum", salary_currency_id_enum)

    # Job experience year
    job_experience_year_list = metadata["experience"]
    job_experience_year_id_enum = {
        job_experience_year["nameEn"]: job_experience_year["id"]
        for job_experience_year in job_experience_year_list
    }
    enums["job_experience_year"] = Enum(
        "JobExperienceYearIdEnum", job_experience_year_id_enum
    )

    # Working arrangement
    working_arrangement_list = metadata["locationType"]
    working_arrangement_id_enum = {
        working_arrangement["nameEn"]: working_arrangement["id"]
        for working_arrangement in working_arrangement_list
    }
    enums["working_arrangement"] = Enum(
        "WorkingArrangmentIdEnum", working_arrangement_id_enum
    )

    # Employment type
    employment_type_list = metadata["employmentType"]
    employment_type_id_enum = {
        employment_type["nameEn"]: employment_type["id"]
        for employment_type in employment_type_list
    }
    enums["employment_type"] = Enum("EmploymentTypeIdEnum", employment_type_id_enum)

    # Job level
    job_level_list = metadata["jobLevels"]
    job_level_id_enum = {
        job_level["nameEn"]: job_level["id"] for job_level in job_level_list
    }
    enums["job_level"] = Enum("JobLevelIdEnum", job_level_id_enum)

    # Education level
    education_level_list = metadata["educations"]
    education_level_id_enum = {
        education_level["nameEn"]: education_level["id"]
        for education_level in education_level_list
    }
    enums["education_level"] = Enum("EducationLevelIdEnum", education_level_id_enum)

    # Working day
    working_day_list = metadata["workingDay"]
    working_day_id_enum = {
        working_day["nameEn"]: working_day["id"] for working_day in working_day_list
    }
    enums["working_day"] = Enum("WorkingDayIdEnum", working_day_id_enum)

    _job_extra_info_cache[cache_key] = enums
    return enums


async def postprocess_job_extra_info(job_extra_info, metadata):
    import hashlib
    import json
    import gc

    # Create hash for caching metadata
    metadata_str = json.dumps(metadata, sort_keys=True)
    metadata_hash = hashlib.md5(
        metadata_str.encode(), usedforsecurity=False
    ).hexdigest()

    # Get all cached enums
    enums = _create_cached_job_extra_info_enums(metadata_hash, metadata_str)
    SalaryPaymentPeriodIdEnum = enums["salary_payment_period"]
    salary_payment_period = (
        job_extra_info.get("salary_payment_period")
        if job_extra_info.get("salary_payment_period")
        else None
    )
    if salary_payment_period:
        salary_payment_period_value = job_extra_info["salary_payment_period"]
        job_extra_info["salary_payment_period"] = {
            "salary_payment_period": salary_payment_period_value,
            "salary_payment_period_id": SalaryPaymentPeriodIdEnum[
                salary_payment_period_value
            ].value,
        }

    SalaryCurrencyIdEnum = enums["salary_currency"]
    salary_currency = (
        job_extra_info.get("salary_currency")
        if job_extra_info.get("salary_currency")
        else None
    )
    if salary_currency:
        salary_currency_value = job_extra_info["salary_currency"].title()
        if salary_currency_value not in ["Usd", "Vnd"]:
            salary_amount = (
                job_extra_info.get("salary_amount")
                if job_extra_info.get("salary_amount")
                else None
            )

            if salary_amount:
                exchange_rate = await get_currency_exchange_rate(
                    src="VND", dst=salary_currency_value.upper()
                )
                if exchange_rate:
                    salary_min = (
                        salary_amount.get("salary_min")
                        if salary_amount.get("salary_min")
                        else None
                    )
                    if salary_min:
                        exchanged_salary_min = salary_min // exchange_rate
                        job_extra_info["salary_amount"]["salary_min"] = (
                            exchanged_salary_min
                        )
                    salary_max = (
                        salary_amount.get("salary_max")
                        if salary_amount.get("salary_max")
                        else None
                    )
                    if salary_max:
                        exchanged_salary_max = salary_max // exchange_rate
                        job_extra_info["salary_amount"]["salary_max"] = (
                            exchanged_salary_max
                        )
                else:
                    job_extra_info["salary_amount"]["salary_min"] = 0.0
                    job_extra_info["salary_amount"]["salary_max"] = 0.0

            salary_currency_value = "Vnd"
        job_extra_info["salary_currency"] = {
            "salary_currency": salary_currency_value,
            "salary_currency_id": SalaryCurrencyIdEnum[salary_currency_value].value,
        }

    JobExperienceYearIdEnum = enums["job_experience_year"]
    job_experience_year = (
        job_extra_info.get("job_experience_year")
        if job_extra_info.get("job_experience_year")
        else None
    )
    if job_experience_year:
        job_experience_year_value = job_extra_info["job_experience_year"]
        job_extra_info["job_experience_year"] = {
            "job_experience_year": job_experience_year_value,
            "job_experience_year_id": JobExperienceYearIdEnum[
                job_experience_year_value
            ].value,
        }

    WorkingArrangmentIdEnum = enums["working_arrangement"]
    working_arrangement = (
        job_extra_info.get("working_arrangement")
        if job_extra_info.get("working_arrangement")
        else None
    )
    if working_arrangement:
        working_arrangement_value = job_extra_info["working_arrangement"]
        job_extra_info["working_arrangement"] = {
            "working_arrangement": working_arrangement_value,
            "working_arrangement_id": WorkingArrangmentIdEnum[
                working_arrangement_value
            ].value,
        }

    EmploymentTypeIdEnum = enums["employment_type"]
    employment_type = (
        job_extra_info.get("employment_type")
        if job_extra_info.get("employment_type")
        else None
    )
    if employment_type:
        employment_type_value = job_extra_info["employment_type"]
        job_extra_info["employment_type"] = {
            "employment_type": employment_type_value,
            "employment_type_id": EmploymentTypeIdEnum[employment_type_value].value,
        }

    JobLevelIdEnum = enums["job_level"]
    job_level = (
        job_extra_info.get("job_level") if job_extra_info.get("job_level") else None
    )
    if job_level:
        job_level_value = job_extra_info["job_level"]
        job_extra_info["job_level"] = {
            "job_level": job_level_value,
            "job_level_id": JobLevelIdEnum[job_level_value].value,
        }

    EducationLevelIdEnum = enums["education_level"]
    education_level = (
        job_extra_info.get("education_level")
        if job_extra_info.get("education_level")
        else None
    )
    if education_level:
        education_level_value = job_extra_info["education_level"]
        job_extra_info["education_level"] = {
            "education_level": education_level_value,
            "education_level_id": EducationLevelIdEnum[education_level_value].value,
        }

    WorkingDayIdEnum = enums["working_day"]
    working_day = (
        job_extra_info.get("working_day") if job_extra_info.get("working_day") else []
    )
    new_working_day = []
    for data in working_day:
        new_working_day.append(
            {
                "working_day": data,
                "working_day_id": WorkingDayIdEnum[data].value,
            }
        )
    job_extra_info.update({"working_day": new_working_day if new_working_day else None})

    # Force garbage collection after processing dynamic objects
    gc.collect()
    return job_extra_info
