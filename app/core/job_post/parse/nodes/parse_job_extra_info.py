import logging

from app.core.job_post.parse.model import generate_model_job_extra_info
from app.core.job_post.parse.state import Parse<PERSON><PERSON>PostState
from app.core.job_post.parse.utils import (
    postprocess_job_extra_info,
    postprocess_working_location,
)
from app.container import get_service_container

logger = logging.getLogger(__name__)


async def parse_job_extra_info(state: ParseJobPostState):
    try:
        container = get_service_container()
        llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0.0)
        job_post_content = state.content

        JobExtraInfo = await generate_model_job_extra_info(state.metadata)

        chain = llm.with_structured_output(JobExtraInfo)

        messages = [
            {
                "role": "user",
                "content": f"""Extract the job information from the following job post content (Avoid hallucination):
<job_post_content>
{job_post_content}
</job_post_content>""",
            }
        ]
        response = await chain.ainvoke(messages)

        job_extra_info = response.model_dump(mode="json")

        new_working_location = await postprocess_working_location(
            job_extra_info, state.metadata.get("city", [])
        )
        job_extra_info.update(
            {"working_location": new_working_location if new_working_location else None}
        )
        new_job_extra_info = await postprocess_job_extra_info(
            job_extra_info, state.metadata
        )

        return {"job_extra_info": new_job_extra_info}
    finally:
        pass
