from app.container import get_service_container
from pydantic import BaseModel

from app.core.job_post.parse.prompt import VAL<PERSON>ATE_CONTENT_PROMPT
from app.core.job_post.parse.state import ParseJobPostState


class ValidateJobPost(BaseModel):
    is_validity: bool
    reasoning: str


async def validate_content(state: ParseJobPostState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0)
    chain = llm.with_structured_output(ValidateJobPost)
    content = state.content
    messages = [
        {"role": "system", "content": VALIDATE_CONTENT_PROMPT},
        {
            "role": "user",
            "content": content,
        },
    ]
    response = await chain.ainvoke(messages)
    return {"is_valid_content": response.is_validity}
