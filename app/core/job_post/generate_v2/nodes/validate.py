from app.container import get_service_container
from app.core.job_post.generate_v2.prompt import VALIDATE_JOB_TITLE_PROMPT
from app.core.job_post.generate_v2.state import GenerateJobPostState
from app.core.job_post.generate_v2.validate_job_title import ValidateJobTitle


async def validate_job_title(state: GenerateJobPostState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0)
    messages = [
        {"role": "system", "content": VALIDATE_JOB_TITLE_PROMPT},
        {
            "role": "user",
            "content": state.job_title,
        },
    ]
    chain = llm.with_structured_output(ValidateJobTitle)
    response = await chain.ainvoke(messages)
    return {"is_valid_job_title": response.is_validity}
