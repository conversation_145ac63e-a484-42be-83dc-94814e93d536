from app.container import get_service_container
from pydantic import BaseModel, Field

from app.core.job_post.generate_v2.prompt import SUGGEST_JOB_TITLE_PROMPT
from app.core.job_post.generate_v2.state import GenerateJobPostState


class SuggestJobTitle(BaseModel):
    original_job_title: str = Field(description="Original job title")
    job_title_suggested: str = Field(description="Suggest job title")


async def suggest_job_title(state: GenerateJobPostState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=0.5)
    messages = [
        {"role": "system", "content": SUGGEST_JOB_TITLE_PROMPT},
        {
            "role": "user",
            "content": state.job_title,
        },
    ]
    chain = llm.with_structured_output(SuggestJobTitle)
    response = await chain.ainvoke(messages)
    if state.job_title != response.job_title_suggested:
        return {
            "is_suggested": True,
            "job_title_suggested": response.job_title_suggested,
        }

    return {
        "is_suggested": False,
        "job_title_suggested": response.job_title_suggested,
    }
