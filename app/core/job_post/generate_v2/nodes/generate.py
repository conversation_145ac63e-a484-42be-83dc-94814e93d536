from langchain.prompts import ChatPromptTemplate

from app.core.job_post.generate_v2.job_post_model import (
    create_generate_model,
)
from app.core.job_post.generate_v2.prompt import GENERATE_JOB_POST_PROMPT
from app.core.job_post.generate_v2.state import GenerateJobPostState
from app.core.job_post.parse.utils import (
    postprocess_benefits,
    postprocess_job_extra_info,
    postprocess_working_location,
)
from app.container import get_service_container


async def generate_job_post(state: GenerateJobPostState):
    container = get_service_container()
    llm = container.llm_service.get_client(model="gpt-4o-mini", temperature=1.0)
    user_input = (
        f"Job title: {state.job_title_suggested}\n"
        f"Job details: {state.job_details}\n"
        f"Company information: {state.company_information}\n"
    )
    prompt = ChatPromptTemplate.from_template(
        GENERATE_JOB_POST_PROMPT, template_format="jinja2"
    )
    Job = await create_generate_model(state.metadata)
    chain = llm.with_structured_output(Job)
    messages = [
        {
            "role": "user",
            "content": prompt.format(user_input=user_input, language=state.language),
        }
    ]
    response = await chain.ainvoke(messages)
    job_post_sections = response.model_dump(mode="json")

    job_info = job_post_sections["job_info"]
    benefits = job_info.get("job_benefit") if job_info.get("job_benefit") else []
    new_benefits = postprocess_benefits(benefits, state.metadata.get("benefits", []))
    # Sorted key order in response
    new_job_info = {
        "job_title": job_info.get("job_title", ""),
        "job_description": job_info.get("job_description", "").replace("**", ""),
        "job_mandatory_requirement": job_info.get("job_mandatory_requirement").replace(
            "**", ""
        )
        if job_info.get("job_mandatory_requirement")
        else None,
        "job_should_have_requirement": job_info.get(
            "job_should_have_requirement"
        ).replace("**", "")
        if job_info.get("job_should_have_requirement")
        else None,
    }
    new_job_info.update({"job_benefit": new_benefits if new_benefits else None})
    job_extra_info = job_post_sections["job_extra_info"]
    new_working_location = await postprocess_working_location(
        job_extra_info, state.metadata.get("city", [])
    )
    job_extra_info.update(
        {"working_location": new_working_location if new_working_location else None}
    )
    new_job_extra_info = await postprocess_job_extra_info(
        job_extra_info, state.metadata
    )
    # if not new_job_extra_info.get("salary_amount"):
    #     new_job_extra_info = await postprocess_salary(
    #         new_job_info, new_job_extra_info, state.metadata
    #     )
    return {"job_info": new_job_info, "job_extra_info": new_job_extra_info}
