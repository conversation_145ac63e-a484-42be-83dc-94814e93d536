from app.core.job_post.generate_v2.state import GenerateJobPostState
from app.core.job_post.suggest_job_function.graph import (
    get_suggest_job_function_graph,
)


async def suggest_job_function(state: GenerateJobPostState):
    job_info = state.job_info
    suggest_job_function_graph = get_suggest_job_function_graph()
    response = await suggest_job_function_graph.ainvoke(
        {
            "job_title": job_info.get("job_title", ""),
            "job_description": job_info.get("job_description", ""),
            "job_mandatory_requirement": job_info.get("job_mandatory_requirement"),
            "job_should_have_requirement": job_info.get("job_should_have_requirement")
            if job_info.get("job_should_have_requirement")
            else "",
            "list_job_function": state.metadata.get("jobFunctions", {}).get(
                "jobFunctions", []
            ),
        }
    )
    return {
        "most_related_job_function": response["most_related_job_function"],
        "related_job_function": response["related_job_function"],
    }
