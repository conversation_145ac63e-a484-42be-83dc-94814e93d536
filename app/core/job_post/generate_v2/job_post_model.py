from enum import Enum

from cachetools import TTLCache
from pydantic import Field, create_model

from app.core.job_post.parse.model import (
    _create_job_extra_info_model,
    _create_job_info_model,
)
from app.core.utils import round_to_million
from app.helpers.get_suggest_salary import get_suggest_salary


_salary_enums_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def _create_cached_salary_enums(metadata_hash: str, metadata_str: str):
    """Create cached salary-related enums based on metadata hash"""
    # Check cache first
    cache_key = metadata_hash
    if cache_key in _salary_enums_cache:
        return _salary_enums_cache[cache_key]

    import json

    metadata = json.loads(metadata_str)

    # Create salary payment period enum
    salary_payment_period_list = metadata.get("salaryPaymentPeriod", [])
    salary_payment_period_id_enum = {
        salary_payment_period["nameEn"]: salary_payment_period["id"]
        for salary_payment_period in salary_payment_period_list
    }
    SalaryPaymentPeriodIdEnum = Enum(
        "SalaryPaymentPeriodIdEnum", salary_payment_period_id_enum
    )

    # Create salary currency enum
    salary_currency_list = metadata.get("salaryCurrency", [])
    salary_currency_id_enum = {
        salary_currency["nameEn"]: salary_currency["id"]
        for salary_currency in salary_currency_list
    }
    SalaryCurrencyIdEnum = Enum("SalaryCurrencyIdEnum", salary_currency_id_enum)

    result = (SalaryPaymentPeriodIdEnum, SalaryCurrencyIdEnum)
    _salary_enums_cache[cache_key] = result
    return result


async def postprocess_salary(job_info, job_extra_info, metadata):
    job_title = job_info["job_title"]
    data = {"data": [{"jobId": "", "jobTitle": job_title}]}
    response = await get_suggest_salary(data)
    if response:
        salary_min = response["data"][0]["minSalary"]
        salary_max = response["data"][0]["maxSalary"]
        # Round salary values to nearest million
        salary_min = round_to_million(salary_min)
        salary_max = round_to_million(salary_max)
    else:
        salary_min = 0
        salary_max = 0
    job_extra_info["salary_payment_period"] = {}
    job_extra_info["salary_currency"] = {}
    job_extra_info["salary_amount"] = {}

    # Use cached enum creation
    import hashlib
    import json

    metadata_str = json.dumps(metadata, sort_keys=True)
    metadata_hash = hashlib.md5(
        metadata_str.encode(), usedforsecurity=False
    ).hexdigest()

    SalaryPaymentPeriodIdEnum, SalaryCurrencyIdEnum = _create_cached_salary_enums(
        metadata_hash, metadata_str
    )

    salary_payment_period_value = "Month"
    job_extra_info["salary_payment_period"] = {
        "salary_payment_period": salary_payment_period_value,
        "salary_payment_period_id": SalaryPaymentPeriodIdEnum[
            salary_payment_period_value
        ].value,
    }

    if salary_min and salary_max:
        job_extra_info["salary_amount"]["salary_min"] = salary_min
        job_extra_info["salary_amount"]["salary_max"] = salary_max

    salary_currency_value = "Vnd"
    job_extra_info["salary_currency"] = {
        "salary_currency": salary_currency_value,
        "salary_currency_id": SalaryCurrencyIdEnum[salary_currency_value].value,
    }
    return job_extra_info


_generate_model_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL


def _create_cached_generate_model(metadata_hash: str, metadata_str: str):
    """Create cached generate model based on metadata hash"""
    # Check cache first
    cache_key = metadata_hash
    if cache_key in _generate_model_cache:
        return _generate_model_cache[cache_key]

    import json

    metadata = json.loads(metadata_str)

    # Create models directly since they're now synchronous
    JobInfo = _create_job_info_model(metadata)
    JobExtraInfo = _create_job_extra_info_model(metadata)

    # Create the Job model
    Job = create_model(
        "Job",
        job_info=(JobInfo, Field(...)),
        job_extra_info=(JobExtraInfo, Field(...)),
    )
    _generate_model_cache[cache_key] = Job
    return Job


async def create_generate_model(metadata):
    """Create generate model with caching"""
    import hashlib
    import json

    # Create a stable hash of the metadata structure
    metadata_str = json.dumps(metadata, sort_keys=True)
    metadata_hash = hashlib.md5(
        metadata_str.encode(), usedforsecurity=False
    ).hexdigest()

    return _create_cached_generate_model(metadata_hash, metadata_str)
