from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_skills.nodes import suggest_skills
from app.core.job_post.suggest_skills.state import (
    SuggestSkillsInput,
    SuggestSkillsOutput,
    SuggestSkillsState,
)


# Global cached graph instance
_compiled_graph = None


def get_suggest_skills_graph():
    """Get cached suggest skills graph to prevent creating new graphs for each request."""
    global _compiled_graph

    if _compiled_graph is None:
        # Define a new graph
        workflow = StateGraph(
            SuggestSkillsState,
            input=SuggestSkillsInput,
            output=SuggestSkillsOutput,
        )

        workflow.add_node("suggest_skills", suggest_skills)

        workflow.add_edge(START, "suggest_skills")
        workflow.add_edge("suggest_skills", END)

        _compiled_graph = workflow.compile()
        _compiled_graph.name = "Job Post Suggest Skills"

    return _compiled_graph
