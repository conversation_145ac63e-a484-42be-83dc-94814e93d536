"""
New parse v2 API route using the refactored workflow architecture.

This route demonstrates the new architecture with dependency injection,
context managers, and proper resource management.
"""

from fastapi import APIRouter, Request, UploadFile, status
from fastapi.concurrency import run_in_threadpool
from fastapi.responses import JSONResponse
from tenacity import RetryError

from app.api.routers import create_error_response, create_response
from app.core.job_post import ParseJobPostInput
from app.core.job_post.providers import (
    GraphFactoryDep,
    ParseWorkflowSettingsDep,
    UnifiedCacheDep,
)
from app.exceptions import ParseError
from app.handlers.parse_v2_new import job_post_parse_v2_new
from app.helpers import (
    get_content_from_file,
    get_content_from_google_docs,
    get_content_from_url,
)
from app.providers import (
    BrowserManagerDep,
    FileManagerDep,
    HTTPManagerDep,
    LangFuseHandlerDep,
    SettingsDep,
)

# Create a new router for the refactored parse v2
job_post_v2_new_router = APIRouter(
    prefix="/internal/v2/job-post-new", 
    tags=["Job Post V2 New Architecture"]
)


async def get_content_from_request_v2_new(
    request: Request,
    file: UploadFile | None,
    file_manager: FileManagerDep,
    http_manager: HTTPManagerDep,
    browser_manager: BrowserManagerDep,
    settings: SettingsDep,
) -> tuple[str | None, JSONResponse | None]:
    """Extract content from request with proper resource management."""
    
    # Handle file upload
    if file:
        try:
            async with file_manager.acquire() as file_ctx:
                content = await get_content_from_file(file, file_context=file_ctx)
                return content, None
        except Exception as e:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                f"Failed to process uploaded file: {str(e)}",
            )
    
    # Handle JSON request
    try:
        data = await request.json()
    except Exception:
        return None, create_error_response(
            status.HTTP_400_BAD_REQUEST,
            "Invalid JSON format in request body.",
        )
    
    # Handle direct content
    if "content" in data:
        return data["content"], None
    
    # Handle URL content
    elif "url" in data:
        url = data["url"]
        try:
            if url.startswith("https://docs.google.com/document/d"):
                return await run_in_threadpool(get_content_from_google_docs, url), None
            else:
                async with browser_manager.acquire() as browser:
                    return await get_content_from_url(url, browser_instance=browser), None
        except RetryError:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed to fetch content from URL. Please verify the URL is accessible.",
            )
    
    return None, create_error_response(
        status.HTTP_400_BAD_REQUEST,
        "Invalid request format. Provide 'content' or 'url' in request body.",
    )


@job_post_v2_new_router.post("/parse", response_model=dict)
async def job_post_parse_v2_new_endpoint(
    request: Request,
    graph_factory: GraphFactoryDep,
    cache_service: UnifiedCacheDep,
    settings: ParseWorkflowSettingsDep,
    langfuse_handler: LangFuseHandlerDep,
    file_manager: FileManagerDep,
    http_manager: HTTPManagerDep,
    browser_manager: BrowserManagerDep,
    app_settings: SettingsDep,
    file: UploadFile | None = None,
) -> JSONResponse:
    """
    Parse job post using the new refactored architecture.
    
    This endpoint demonstrates:
    - Dependency injection for all services
    - Workflow context managers for resource management
    - Unified caching system
    - Proper error handling and logging
    """
    
    # Extract content from request
    content, error_response = await get_content_from_request_v2_new(
        request, file, file_manager, http_manager, browser_manager, app_settings
    )
    
    if error_response:
        return error_response
    
    try:
        # Create input data
        data = ParseJobPostInput(content=content)
        
        # Execute parsing with new architecture
        response = await job_post_parse_v2_new(
            data=data,
            graph_factory=graph_factory,
            cache_service=cache_service,
            settings=settings,
            langfuse_handler=langfuse_handler,
        )
        
        if not response:
            return create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Invalid content structure. Please ensure the content follows the required format.",
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data=response,
            ),
        )
        
    except ParseError as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"Parse error: {str(e)}",
        )
    except Exception as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"Unexpected error: {str(e)}",
        )


@job_post_v2_new_router.get("/health")
async def health_check_new():
    """Health check endpoint for the new architecture."""
    return {"status": "healthy", "architecture": "new_refactored"}


@job_post_v2_new_router.get("/stats")
async def get_workflow_stats(
    graph_factory: GraphFactoryDep,
    cache_service: UnifiedCacheDep,
):
    """Get statistics about the workflow system."""
    try:
        graph_stats = await graph_factory.get_registry_stats()
        cache_stats = cache_service.get_stats()
        
        return {
            "graph_registry": graph_stats,
            "cache_service": cache_stats,
        }
    except Exception as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"Failed to get stats: {str(e)}",
        )
