"""
Test route for the new parse v2 architecture.

This route tests the basic infrastructure without full node migration.
"""

from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.routers import create_response
from app.core.job_post.providers import (
    GraphFactoryDep,
    ParseWorkflowSettingsDep,
    UnifiedCacheDep,
)

# Create a test router
job_post_v2_test_router = APIRouter(
    prefix="/internal/v2/job-post-test", 
    tags=["Job Post V2 Test"]
)


@job_post_v2_test_router.get("/health")
async def health_check_test():
    """Health check endpoint for testing the new architecture."""
    return {"status": "healthy", "architecture": "new_refactored_test"}


@job_post_v2_test_router.get("/factory-stats")
async def get_factory_stats(
    graph_factory: GraphFactoryDep,
):
    """Test the graph factory dependency injection."""
    try:
        stats = await graph_factory.get_registry_stats()
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data={
                    "factory_type": type(graph_factory).__name__,
                    "registry_stats": stats,
                }
            ),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": str(e)}
        )


@job_post_v2_test_router.get("/cache-stats")
async def get_cache_stats(
    cache_service: UnifiedCacheDep,
):
    """Test the unified cache service dependency injection."""
    try:
        stats = cache_service.get_stats()
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data={
                    "cache_type": type(cache_service).__name__,
                    "cache_stats": stats,
                }
            ),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": str(e)}
        )


@job_post_v2_test_router.get("/settings")
async def get_workflow_settings(
    settings: ParseWorkflowSettingsDep,
):
    """Test the workflow settings dependency injection."""
    try:
        # Convert settings to dict for JSON response
        settings_dict = {
            field.name: getattr(settings, field.name)
            for field in settings.__dataclass_fields__.values()
        }
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data={
                    "settings_type": type(settings).__name__,
                    "settings": settings_dict,
                }
            ),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": str(e)}
        )


@job_post_v2_test_router.post("/test-cache")
async def test_cache_operations(
    cache_service: UnifiedCacheDep,
):
    """Test basic cache operations."""
    try:
        # Test cache set
        test_key = "test_key"
        test_value = {"test": "data", "timestamp": "2024-01-01"}
        
        set_result = await cache_service.set("test", test_key, test_value)
        
        # Test cache get
        get_result = await cache_service.get("test", test_key)
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data={
                    "set_success": set_result,
                    "get_result": get_result,
                    "cache_stats": cache_service.get_stats(),
                }
            ),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": str(e)}
        )
