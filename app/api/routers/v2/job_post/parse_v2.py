from __future__ import annotations


from fastapi import APIRouter, Request, UploadFile, status
from fastapi.concurrency import run_in_threadpool
from fastapi.responses import JSONResponse
from tenacity import RetryError

from app.api.routers import create_error_response, create_response
from app.core.job_post import ParseJobPostInput
from app.core.job_post.parse_v2.graph import get_parse_v2_graph
from app.exceptions import ParseError
from app.handlers import job_post_parse_v2 as job_post_parse_handler
from app.helpers import (
    get_content_from_file,
    get_content_from_google_docs,
    get_content_from_url,
)
from app.providers import (
    BrowserManagerDep,
    CacheServiceDep,
    FileManagerDep,
    HTTPManagerDep,
    SettingsDep,
)

job_post_v2_router = APIRouter(prefix="/internal/v2/job-post", tags=["Job Post V2"])


async def get_content_from_request_v2(
    request: Request,
    file: UploadFile | None,
    file_manager: <PERSON><PERSON>anagerDep,
    http_manager: <PERSON><PERSON><PERSON><PERSON>gerDep,
    browser_manager: BrowserManagerDep,
    settings: SettingsDep,
) -> tuple[str | None, JSONResponse | None]:
    """Extract content from request with proper resource management."""
    
    # Handle file upload
    form = await request.form()
    if form and file:
        if file.size > settings.file.max_file_size:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                f"File size exceeds maximum limit of {settings.file.max_file_size // (1024*1024)}MB.",
            )
        
        try:
            async with file_manager.acquire() as temp_file:
                content = await file.read()
                await temp_file.open("wb")
                await temp_file.write(content)
                await temp_file.close()
                
                return await get_content_from_file(file), None
        except RetryError:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed to process uploaded file. Please ensure the file is valid and try again.",
            )
    
    # Handle JSON request
    try:
        data = await request.json()
    except Exception:
        return None, create_error_response(
            status.HTTP_400_BAD_REQUEST,
            "Invalid JSON format in request body.",
        )
    
    # Handle direct content
    if "content" in data:
        return data["content"], None
    
    # Handle URL content
    elif "url" in data:
        url = data["url"]
        try:
            if url.startswith("https://docs.google.com/document/d"):
                return await run_in_threadpool(get_content_from_google_docs, url), None
            else:
                async with browser_manager.acquire() as browser:
                    return await get_content_from_url(url, browser_instance=browser), None
        except RetryError:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed to fetch content from URL. Please verify the URL is accessible.",
            )
    
    return None, create_error_response(
        status.HTTP_400_BAD_REQUEST,
        "Invalid request format. Provide 'content' or 'url' in request body.",
    )


@job_post_v2_router.post("/parse", response_model=dict)
async def job_post_parse_v2(
    request: Request,
    cache_service: CacheServiceDep,
    file_manager: FileManagerDep,
    http_manager: HTTPManagerDep,
    browser_manager: BrowserManagerDep,
    settings: SettingsDep,
    file: UploadFile | None = None,
) -> JSONResponse:
    """Parse job post with dependency injection and context managers."""
    
    content, error_response = await get_content_from_request_v2(
        request, file, file_manager, http_manager, browser_manager, settings
    )
    
    if error_response:
        return error_response
    
    try:
        data = ParseJobPostInput(content=content)
        graph = await get_parse_v2_graph()
        
        response = await job_post_parse_handler(
            data=data,
            graph=graph,
            cache_service=cache_service,
        )
        
        if not response:
            return create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Invalid content structure.",
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data=response,
            ),
        )
        
    except ParseError as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"Parse error: {str(e)}",
        )
    except Exception as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"Unexpected error: {str(e)}",
        )
