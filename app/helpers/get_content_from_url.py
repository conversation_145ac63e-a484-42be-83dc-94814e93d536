import asyncio
import logging
import os

from crawl4ai import Crawler<PERSON>unConfig

from app.container import get_service_container
from app.exceptions import URLError

logger = logging.getLogger(__name__)


async def get_content_from_url(url: str) -> str:
    """
    Fetch content from URL using shared browser pool to prevent memory leaks.

    - Uses browser pool to reuse browser instances across requests
    - Applies strict timeouts to avoid hanging browser processes
    - Implements proper resource cleanup
    """
    run_config = CrawlerRunConfig(
        excluded_tags=["script", "style", "img", "a", "button", "footer"],
        remove_forms=True,
        prettiify=True,
        verbose=False,  # Reduce logging noise
    )

    # Overall timeout (seconds) for the crawl to finish
    crawl_timeout = int(os.getenv("CRAWL_TIMEOUT", "30"))

    # Get browser pool service
    container = get_service_container()
    browser_pool = container.browser_pool_service

    crawler = None
    try:
        # Get a crawler from the pool
        crawler = await browser_pool.get_crawler()

        # Ensure the crawl cannot hang indefinitely
        try:
            result = await asyncio.wait_for(
                crawler.arun(url=url, config=run_config), timeout=crawl_timeout
            )
        except asyncio.TimeoutError as exc:
            logger.warning("Crawl timeout for url=%s after %ss", url, crawl_timeout)
            raise URLError("Timed out fetching content from URL") from exc

        if result.success:
            content = result.markdown or ""
            return content
        raise URLError(f"Failed to fetch content from URL: {url}")

    except URLError:
        # Bubble up known errors unchanged
        raise
    except Exception as e:  # Defensive: wrap unknown errors
        logger.error("Error fetching content from URL %s: %s", url, str(e))
        raise URLError("Error fetching content from URL") from e
    finally:
        # Always return the crawler to the pool
        if crawler:
            await browser_pool.return_crawler(crawler)
