import asyncio
import os

from cachetools import TTLCache
from tenacity import retry, stop_after_attempt, wait_fixed

from app.container import get_service_container

META_API = os.getenv("META_API")

# TTL cache for get_meta_job_post function
_meta_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL
_meta_lock = asyncio.Lock()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def _get_meta_job_post_uncached():
    """Internal function without caching for retry logic"""
    try:
        http_client = get_service_container().http_client_service
        response = await http_client.client.get(url=META_API, timeout=1)
        if response.status_code == 200:
            return response.json()["data"]
        return []
    except Exception:
        return []


async def get_meta_job_post():
    """Get meta job post data with TTL caching that doesn't cache empty results"""
    cache_key = "meta_job_post"

    async with _meta_lock:
        # Check if result is in cache
        if cache_key in _meta_cache:
            return _meta_cache[cache_key]

    # Call the function with retry logic
    result = await _get_meta_job_post_uncached()

    # Only cache non-empty results
    if not (isinstance(result, list) and len(result) == 0):
        async with _meta_lock:
            _meta_cache[cache_key] = result

    return result
