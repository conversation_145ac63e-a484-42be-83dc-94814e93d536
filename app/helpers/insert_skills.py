import os

from app.container import get_service_container
from app.exceptions import InsertSkillsError

CAREER_RESOURCES_API = os.getenv("CAREER_RESOURCES_API")


async def insert_skills(skills: list[str]) -> list[str]:
    """
    Insert skills into the career resources
    """
    list_skills_input = []
    for skill in skills:
        list_skills_input.append({"name": skill})

    payload = {"objects": list_skills_input}

    http_client = get_service_container().http_client_service
    response = await http_client.client.post(
        f"{CAREER_RESOURCES_API}/internal/v1/skills", json=payload, timeout=10
    )
    if response.status_code != 200:
        raise InsertSkillsError(f"Failed to insert skills: {response.text}")
    skills_required = []
    for skill in response.json()["data"]:
        skills_required.append({"skill_id": skill["id"], "skill_name": skill["name"]})
    return skills_required
