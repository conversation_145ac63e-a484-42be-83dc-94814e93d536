import asyncio
import os

from cachetools import TTLCache
from tenacity import retry, stop_after_attempt, wait_fixed

from app.container import get_service_container

LOCATION_API = os.getenv("LOCATION_API")

# TTL caches for location functions
_country_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL
_city_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL
_district_cache = TTLCache(maxsize=10, ttl=300)  # 5 minutes TTL
_location_lock = asyncio.Lock()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def _get_country_uncached():
    """Internal function without caching for retry logic"""
    try:
        http_client = get_service_container().http_client_service
        response = await http_client.client.get(
            url=f"{LOCATION_API}/country/list?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c",
            timeout=1,
        )
        if response.status_code == 200:
            return response.json()["data"]
        return []
    except Exception:
        return []


async def get_country():
    """Get country data with TTL caching that doesn't cache empty results"""
    cache_key = "country_list"

    async with _location_lock:
        if cache_key in _country_cache:
            return _country_cache[cache_key]

    result = await _get_country_uncached()

    if not (isinstance(result, list) and len(result) == 0):
        async with _location_lock:
            _country_cache[cache_key] = result

    return result


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def _get_city_uncached(country_id: str):
    """Internal function without caching for retry logic"""
    try:
        http_client = get_service_container().http_client_service
        response = await http_client.client.get(
            url=f"{LOCATION_API}/country/{country_id}?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c",
            timeout=1,
        )
        if response.status_code == 200:
            return response.json()["data"]
        return []
    except Exception:
        return []


async def get_city(country_id: str):
    """Get city data with TTL caching that doesn't cache empty results"""
    cache_key = f"city_{country_id}"

    async with _location_lock:
        if cache_key in _city_cache:
            return _city_cache[cache_key]

    result = await _get_city_uncached(country_id)

    if not (isinstance(result, list) and len(result) == 0):
        async with _location_lock:
            _city_cache[cache_key] = result

    return result


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def _get_district_uncached(city_id: str):
    """Internal function without caching for retry logic"""
    try:
        http_client = get_service_container().http_client_service
        response = await http_client.client.get(
            url=f"{LOCATION_API}/city/{city_id}?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c",
            timeout=1,
        )
        if response.status_code == 200:
            return response.json()["data"]
        return []
    except Exception:
        return []


async def get_district(city_id: str):
    """Get district data with TTL caching that doesn't cache empty results"""
    cache_key = f"district_{city_id}"

    async with _location_lock:
        if cache_key in _district_cache:
            return _district_cache[cache_key]

    result = await _get_district_uncached(city_id)

    if not (isinstance(result, list) and len(result) == 0):
        async with _location_lock:
            _district_cache[cache_key] = result

    return result
