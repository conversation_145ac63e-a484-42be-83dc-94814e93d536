import os
from base64 import b64encode

from fastapi import UploadFile
from tenacity import retry, stop_after_attempt, wait_fixed

from app.container import get_service_container
from app.exceptions import FileError

TEXTRACT_API = os.getenv("TEXTRACT_API")
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", str(10 * 1024 * 1024)))  # Default 100MB


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_content_from_file(file: UploadFile):
    try:
        # Check file size before reading
        if file.size and file.size > MAX_FILE_SIZE:
            raise FileError(
                f"File too large: {file.size} bytes. Maximum allowed: {MAX_FILE_SIZE} bytes"
            )

        # Read file in chunks to avoid memory issues with large files
        byte_content = await file.read()
        filename = file.filename or "unknown"
        base64_string = b64encode(byte_content).decode("utf-8")
        raw_data = {
            "data": base64_string,
            "file_type": os.path.splitext(filename)[1][1:] if filename else "txt",
        }
        if not TEXTRACT_API:
            raise FileError("TEXTRACT_API is not configured")

        http_client = get_service_container().http_client_service
        response = await http_client.client.post(
            url=TEXTRACT_API, json=raw_data, timeout=30
        )

        if response.status_code == 200:
            content = response.json().get("text", "")
            return content
        else:
            raise FileError("Get content from file error!")
    finally:
        await file.close()
