import os

from app.container import get_service_container

SALARY_API = os.getenv("SALARY_API")


async def get_suggest_salary(data):
    try:
        http_client = get_service_container().http_client_service
        response = await http_client.client.post(url=SALARY_API, json=data, timeout=3)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception:
        return None
