"""
Service container for managing application dependencies.

Provides a centralized way to manage service instances and their lifecycle.
"""

import logging
from typing import Optional

from langfuse.callback import Callback<PERSON>andler

from app.core.llm import LLMService
from app.services.browser_pool import BrowserPoolService
from app.services.cache import CacheService
from app.services.http_client import HTTPClientService
from app.services.redis import RedisService

logger = logging.getLogger(__name__)


class ServiceContainer:
    """Container for managing application services with proper lifecycle."""

    def __init__(self):
        self._redis_service: Optional[RedisService] = None
        self._cache_service: Optional[CacheService] = None
        self._llm_service: Optional[LLMService] = None
        self._http_client_service: Optional[HTTPClientService] = None
        self._browser_pool_service: Optional[BrowserPoolService] = None
        self._langfuse_handler: Optional[CallbackHandler] = None

    @property
    def redis_service(self) -> RedisService:
        """Get Redis service instance (lazy initialization)."""
        if self._redis_service is None:
            self._redis_service = RedisService()
            logger.debug("Redis service initialized")
        return self._redis_service

    @property
    def cache_service(self) -> CacheService:
        """Get cache service instance (lazy initialization)."""
        if self._cache_service is None:
            self._cache_service = CacheService(self.redis_service)
            logger.debug("Cache service initialized")
        return self._cache_service

    @property
    def llm_service(self) -> LLMService:
        """Get LLM service instance (lazy initialization)."""
        if self._llm_service is None:
            self._llm_service = LLMService()
            logger.debug("LLM service initialized")
        return self._llm_service

    @property
    def http_client_service(self) -> HTTPClientService:
        """Get HTTP client service instance (lazy initialization)."""
        if self._http_client_service is None:
            self._http_client_service = HTTPClientService()
            logger.debug("HTTP client service initialized")
        return self._http_client_service

    @property
    def browser_pool_service(self) -> BrowserPoolService:
        """Get browser pool service instance (lazy initialization)."""
        if self._browser_pool_service is None:
            self._browser_pool_service = BrowserPoolService(pool_size=3)
            logger.debug("Browser pool service initialized")
        return self._browser_pool_service

    @property
    def langfuse_handler(self) -> CallbackHandler:
        """Get LangFuse callback handler instance (lazy initialization)."""
        if self._langfuse_handler is None:
            self._langfuse_handler = CallbackHandler()
            logger.debug("LangFuse handler initialized")
        return self._langfuse_handler

    async def startup(self):
        """Initialize services during application startup."""
        logger.info("Service container ready")

    async def shutdown(self):
        """Cleanup services during application shutdown."""
        logger.info("Shutting down service container...")

        cleanup_tasks = []

        if self._browser_pool_service:
            cleanup_tasks.append(
                ("Browser pool service", self._browser_pool_service.cleanup())
            )

        if self._http_client_service:
            cleanup_tasks.append(
                (
                    "HTTP client service",
                    self._http_client_service.cleanup(),
                )
            )

        if self._llm_service:
            cleanup_tasks.append(("LLM service", self._llm_service.cleanup()))

        if self._cache_service:
            cleanup_tasks.append(("Cache service", self._cache_service.cleanup()))

        if self._redis_service:
            cleanup_tasks.append(("Redis service", self._redis_service.close()))

        # LangFuse handler doesn't require async cleanup, just clear reference
        if self._langfuse_handler:
            self._langfuse_handler = None
            logger.debug("LangFuse handler cleaned up")

        for name, task in cleanup_tasks:
            try:
                await task
                logger.debug(f"{name} cleanup completed")
            except Exception as e:
                logger.error(f"Error during {name} cleanup: {e}")

        logger.info("Service container shutdown complete")


# Global service container instance
_service_container: Optional[ServiceContainer] = None


def get_service_container() -> ServiceContainer:
    """Get the global service container instance."""
    global _service_container
    if _service_container is None:
        _service_container = ServiceContainer()
    return _service_container
