# Agent Instructions for Upzi Opportunity Creation

## Build/Lint/Test Commands

### Development Setup
- Install dependencies: `uv sync --dev`
- Install all deps: `uv sync`

### Code Quality
- Format and lint: `uv run ruff check --fix && ruff format`
- Lint only: `uv run ruff check`
- Pre-commit hooks: `uv run pre-commit run --all-files`

### Testing
- Run all tests: `uv run pytest`
- Run specific test: `uv run pytest tests/test_file.py::TestClass::test_method`

### Running the Application
- Development: `uv run python main.py`
- Production: `uv run uvicorn main:app --host 0.0.0.0 --port 8080`

## Code Style Guidelines

### Python Standards
- Python 3.13+ with type hints required
- Use `from __future__ import annotations` for forward references
- Follow PEP 8 conventions

### Imports
- Standard library imports first
- Third-party imports second
- Local imports last
- One import per line
- Group imports logically with blank lines

### Naming Conventions
- Functions/variables: `snake_case`
- Classes: `PascalCase`
- Constants: `UPPER_SNAKE_CASE`
- Private methods: `_leading_underscore`

### Type Hints & Dataclasses
- Use type hints for all function parameters and return values
- Prefer dataclasses with `kw_only=True` for configuration
- Use `Optional[T]` for nullable types
- Use `Union[T1, T2]` for multiple possible types

### Error Handling
- Use custom exceptions from `app.exceptions`
- Include descriptive error messages
- Log errors with appropriate levels
- Implement proper resource cleanup in context managers

### Documentation
- Add docstrings to all classes and public functions
- Use triple quotes with proper formatting
- Document parameters, return values, and exceptions

### Logging & Monitoring
- Use structured logging with `logging.getLogger(__name__)`
- Include memory monitoring and cleanup patterns
- Follow existing resource cleanup patterns in lifespan events

### Security
- Never log sensitive information (API keys, passwords)
- Use environment variables for configuration
- Follow security best practices for file handling
