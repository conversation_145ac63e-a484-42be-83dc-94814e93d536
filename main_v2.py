from __future__ import annotations

import asyncio
import gc
import logging
import logging.config
import os
from contextlib import asynccontextmanager

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.routers.health import health_router
from app.api.routers.v1.job_post import parse_router
from app.api.routers.v2.job_post import (
    generate_router_v2,
    parse_router_v2,
    parse_test_router_v2,
)
from app.config import get_settings
from app.middleware.limits import create_request_limits_middleware
from app.middleware.memory import create_memory_monitoring_middleware
from app.providers.dependencies import (
    get_database_manager,
    get_file_manager,
)
from app.utils import LOGGING_CONFIG

# Load environment variables
load_dotenv()

# Setup logging
os.makedirs("logs", exist_ok=True)
logging.config.dictConfig(LOGGING_CONFIG)

logger = logging.getLogger(__name__)


async def periodic_memory_cleanup(settings):
    """Periodic memory cleanup with configuration-based settings."""
    import psutil

    process = psutil.Process(os.getpid())

    while True:
        try:
            await asyncio.sleep(settings.memory.cleanup_interval)

            memory_mb = process.memory_info().rss / 1024 / 1024
            logger.info(f"Current memory usage: {memory_mb:.2f}MB")

            if memory_mb > settings.memory.warning_threshold_mb:
                logger.warning(f"High memory usage: {memory_mb:.2f}MB")

                # Perform garbage collection
                gc_passes = (
                    settings.memory.gc_passes_critical
                    if memory_mb > settings.memory.critical_threshold_mb
                    else settings.memory.gc_passes_normal
                )

                collected = 0
                for _ in range(gc_passes):
                    collected += gc.collect()

                logger.info(f"Garbage collection freed {collected} objects")

        except asyncio.CancelledError:
            logger.info("Memory cleanup task cancelled")
            break
        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan with resource management."""
    settings = get_settings()

    # Startup
    logger.info("Starting application with new architecture...")

    # Initialize managers
    db_manager = get_database_manager(settings)
    file_manager = get_file_manager(settings)

    # Start background tasks
    file_manager.start_cleanup_task()
    cleanup_task = asyncio.create_task(periodic_memory_cleanup(settings))

    logger.info("Application startup completed")

    yield

    # Shutdown
    logger.info("Shutting down application...")

    # Stop background tasks
    cleanup_task.cancel()
    await file_manager.stop_cleanup_task()

    # Cleanup managers
    await db_manager.close_pool()

    # Wait for cleanup task
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass

    logger.info("Application shutdown completed")


def create_app() -> FastAPI:
    """Create FastAPI application with best practices."""
    settings = get_settings()

    app = FastAPI(
        title="Upzi Opportunity Creation",
        description="AI-powered job opportunity creation and parsing service",
        version="2.0.0",
        lifespan=lifespan,
    )

    # Add middleware with configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add custom middleware
    app.middleware("http")(create_memory_monitoring_middleware(settings))
    app.middleware("http")(create_request_limits_middleware(settings))

    # Include routers
    app.include_router(health_router)
    app.include_router(parse_router)
    app.include_router(parse_router_v2)
    app.include_router(generate_router_v2)
    app.include_router(parse_test_router_v2)  # Test router for new architecture

    return app


# Create app instance
app = create_app()


if __name__ == "__main__":
    settings = get_settings()

    uvicorn.run(
        app="main_v2:app",
        host=settings.host,
        port=settings.port,
        log_config=LOGGING_CONFIG,
        reload=settings.debug,
    )
