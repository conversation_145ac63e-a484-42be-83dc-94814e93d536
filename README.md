# Upzi Opportunity Creation

An AI-powered job posting service that uses LangGraph workflows to parse, generate, and enhance job opportunities. Built with FastAPI and designed for high performance and reliability.

## Features

- **Job Post Parsing**: Extract structured information from job descriptions via text, URLs, Google Docs, or file uploads
- **Job Post Generation**: Create comprehensive job postings from basic job titles with AI-powered enhancement
- **Content Suggestion**: AI-driven suggestions for skills, job functions, and content improvements
- **Multi-format Input**: Support for direct text, URLs, Google Docs, and file uploads (up to 5MB)
- **Versioned APIs**: Both V1 and V2 endpoints for backward compatibility and feature evolution
- **Caching**: Redis-based caching for improved performance
- **Observability**: Sentry for error monitoring

## API Endpoints

### Health Check
- `GET /health` - Service health status

### V1 Job Post API
- `POST /internal/v1/job-post/parse` - Parse job posting content

### V2 Job Post API
- `POST /internal/v2/job-post/parse` - Enhanced job posting parsing
- `POST /internal/v2/job-post/generate` - Generate job postings from job titles

## Installation

### Prerequisites
- Python 3.13+
- Redis server
- OpenAI API key

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd upzi-opportunity-creation
   ```

2. **Install dependencies**
   ```bash
   uv sync --dev
   ```

3. **Environment configuration**
   Create a `.env` file with the following variables:
   ```env
   # OpenAI Configuration
   OPENAI_API_KEY=your_openai_api_key

   # Redis Configuration
   REDIS_URL=redis://localhost:6379

   # Application Settings
   APP_HOST=0.0.0.0
   APP_PORT=8080
   APP_ENVIRONMENT=dev

   # Monitoring (Optional)
   SENTRY_DSN=your_sentry_dsn
   ```

4. **Run the application**
   ```bash
   # Development (with auto-reload)
   python main.py

   # Production
   uvicorn main:app --host 0.0.0.0 --port 8080
   ```

## Development

### Code Quality
```bash
# Format and lint code
ruff check --fix
ruff format

# Run linting only
ruff check
```

### Testing
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_specific.py
```

### Project Structure

```
app/
├── api/                    # FastAPI routers and endpoints
│   └── routers/
│       ├── health.py       # Health check endpoint
│       ├── v1/job_post/    # V1 job post endpoints
│       └── v2/job_post/    # V2 job post endpoints
├── core/                   # Core business logic
│   ├── job_post/          # Job posting workflows
│   │   ├── generate_v2/   # Job generation workflow
│   │   ├── parse/         # Job parsing workflow
│   │   └── suggest_*/     # Suggestion workflows
│   └── llm_client.py      # LLM client management
├── handlers/              # Request handlers
├── helpers/               # Utility functions
├── services/              # Infrastructure services
└── utils/                 # Common utilities
```

## Usage Examples

### Parse Job Posting from Text
```bash
curl -X POST "http://localhost:8080/internal/v2/job-post/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Software Engineer position requiring Python, FastAPI, and AI experience..."
  }'
```

### Parse Job Posting from URL
```bash
curl -X POST "http://localhost:8080/internal/v2/job-post/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/job-posting"
  }'
```

### Generate Job Posting
```bash
curl -X POST "http://localhost:8080/internal/v2/job-post/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Senior Python Developer"
  }'
```

### Upload File for Parsing
```bash
curl -X POST "http://localhost:8080/internal/v2/job-post/parse" \
  -F "file=@job_description.pdf"
```

## Architecture

The service uses **LangGraph** workflows for processing job-related data:

- **State-based Processing**: Each workflow maintains typed state for data flow
- **Node-based Architecture**: Individual processing steps as reusable nodes
- **Conditional Routing**: Smart workflow routing based on validation results
- **Retry Mechanisms**: Built-in retry policies for robust error handling

### Key Workflows

1. **Job Post Generation** (`generate_v2/`):
   - Job title validation
   - Content generation
   - Skills and function suggestion
   - Post-processing and formatting

2. **Job Post Parsing** (`parse/`):
   - Content validation
   - Information extraction
   - Enhancement suggestions
   - Structured output generation

## Monitoring & Observability

- **Memory Monitoring**: Automatic memory usage tracking and cleanup
- **Sentry Integration**: Error tracking and alerting
- **Structured Logging**: Comprehensive logging with file rotation

## License

[License information here]
