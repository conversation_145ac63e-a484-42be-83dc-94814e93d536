[project]
name = "upzi-opportunity-creation"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "cachetools>=6.2.0",
    "crawl4ai>=0.7.4",
    "fastapi>=0.116.1",
    "gdown>=5.2.0",
    "langchain>=0.3.27",
    "langchain-openai>=0.3.31",
    "langfuse==2.60.1",
    "langgraph>=0.6.6",
    "langid>=1.1.6",
    "playwright>=1.54.0",
    "psutil>=7.0.0",
    "python-docx>=1.2.0",
    "python-multipart>=0.0.20",
    "redis>=6.4.0",
    "sentry-sdk>=2.35.0",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "pre-commit>=4.3.0",
    "pytest>=8.4.1",
    "ruff>=0.12.10",
]
